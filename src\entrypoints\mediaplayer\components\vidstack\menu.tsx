import { Menu } from "@vidstack/react";
// See "Icons" component page for setup before importing the following:
import { SettingsIcon } from "@vidstack/react/icons";

type MenuProps = {
  children: React.ReactNode;
};

export function SettingsMenu({ children }: MenuProps) {
  return (
    <Menu.Root className="vds-menu">
      <Menu.Button
        className="p-3 rounded-full bg-slate-800/50 hover:bg-slate-700/50 transition-all duration-200 hover:scale-105 flex items-center justify-center"
        aria-label="Settings"
      >
        <SettingsIcon className="w-6 h-6 text-slate-300" />
      </Menu.Button>
      <Menu.Items className="vds-menu-items" placement="top" offset={0}>
        {children}
      </Menu.Items>
    </Menu.Root>
  );
}
