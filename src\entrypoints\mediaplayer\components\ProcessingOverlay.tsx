import React from "react";
import { Loader2 } from "lucide-react";

interface ProcessingOverlayProps {
  isVisible: boolean;
  message?: string;
}

export const ProcessingOverlay = ({
  isVisible,
  message = "Processing...",
}: ProcessingOverlayProps) => {
  if (!isVisible) return null;

  return (
    <div className="absolute inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center">
      <div className="bg-slate-900 border border-slate-700 rounded-xl p-8 max-w-sm w-full mx-4">
        <div className="text-center">
          {/* Spinning Loader */}
          <div className="w-16 h-16 mx-auto mb-6">
            <Loader2 className="w-16 h-16 text-blue-500 animate-spin" />
          </div>

          {/* Status Text */}
          <h3 className="text-lg font-semibold text-white mb-2">{message}</h3>
          <p className="text-sm text-slate-400">
            Please wait while we process your file
          </p>
        </div>
      </div>
    </div>
  );
};
