import { create } from "zustand";
import { persist } from "zustand/middleware";

interface SettingsState {
  // Streaming settings
  enableStreaming: boolean;
  chunkDurationSeconds: number;
  
  // Actions
  setEnableStreaming: (enabled: boolean) => void;
  setChunkDurationSeconds: (seconds: number) => void;
  
  // Utility
  resetToDefaults: () => void;
}

const defaultSettings = {
  enableStreaming: false, // Default to false for safety
  chunkDurationSeconds: 10,
};

export const useSettings = create<SettingsState>()(
  persist(
    (set) => ({
      ...defaultSettings,
      
      setEnableStreaming: (enabled: boolean) => 
        set({ enableStreaming: enabled }),
      
      setChunkDurationSeconds: (seconds: number) => 
        set({ chunkDurationSeconds: Math.max(5, Math.min(30, seconds)) }), // Clamp between 5-30 seconds
      
      resetToDefaults: () => set(defaultSettings),
    }),
    {
      name: "webplayer-settings", // Storage key
      version: 1,
    }
  )
);

/**
 * Hook to check if streaming should be enabled for a given file
 */
export const useStreamingEnabled = (file: File | string, mediaType: "video" | "audio") => {
  const enableStreaming = useSettings((state) => state.enableStreaming);
  
  // Only enable streaming for video files and local files
  const shouldEnable = enableStreaming && 
    mediaType === "video" && 
    typeof file !== "string" && // Only local files for now
    window.MediaSource; // Browser support check
    
  return shouldEnable;
};
