import React from "react";
import { Setting<PERSON>, Zap, Play } from "lucide-react";
import { useSettings } from "@/lib/settings";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";

export const SettingsDropdown = () => {
  const {
    enableStreaming,
    chunkDurationSeconds,
    setEnableStreaming,
    setChunkDurationSeconds,
  } = useSettings();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button
          className="flex items-center gap-2 p-2 text-gray-400 hover:text-white transition-colors rounded-lg hover:bg-slate-700"
          title="Streaming Settings"
        >
          <Settings className="w-5 h-5" />
        </button>
      </DropdownMenuTrigger>

      <DropdownMenuContent
        className="w-80 bg-slate-800 border-slate-700"
        align="end"
        sideOffset={8}
      >
        <div className="p-4">
          {/* Header */}
          <DropdownMenuLabel className="flex items-center gap-2 text-white pb-2">
            <Zap className="w-4 h-4 text-yellow-400" />
            Streaming Settings
          </DropdownMenuLabel>

          <DropdownMenuSeparator className="bg-slate-700" />

          {/* Enable Streaming Toggle */}
          <div className="py-3">
            <div className="flex items-center justify-between mb-2">
              <label className="text-white text-sm font-medium">
                Enable Streaming
              </label>
              <button
                onClick={() => setEnableStreaming(!enableStreaming)}
                className={`relative inline-flex h-5 w-9 items-center rounded-full transition-colors ${
                  enableStreaming ? "bg-blue-600" : "bg-gray-600"
                }`}
              >
                <span
                  className={`inline-block h-3 w-3 transform rounded-full bg-white transition-transform ${
                    enableStreaming ? "translate-x-5" : "translate-x-1"
                  }`}
                />
              </button>
            </div>
            <p className="text-xs text-gray-400">
              Start playback while transcoding (video only)
            </p>
          </div>

          {/* Chunk Duration Slider */}
          {enableStreaming && (
            <div className="py-3">
              <label className="block text-white text-sm font-medium mb-2">
                Chunk Duration: {chunkDurationSeconds}s
              </label>
              <input
                type="range"
                min="5"
                max="30"
                step="1"
                value={chunkDurationSeconds}
                onChange={(e) =>
                  setChunkDurationSeconds(parseInt(e.target.value))
                }
                className="w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer"
              />
              <div className="flex justify-between text-xs text-gray-400 mt-1">
                <span>5s</span>
                <span>30s</span>
              </div>
            </div>
          )}

          <DropdownMenuSeparator className="bg-slate-700" />

          {/* Status Info */}
          <div className="py-3">
            <div className="flex items-start gap-2">
              <Play className="w-3 h-3 text-blue-400 mt-0.5 flex-shrink-0" />
              <div className="text-xs text-gray-300">
                <p className="font-medium mb-1">Status:</p>
                <div className="space-y-1">
                  <div className="flex justify-between">
                    <span>Streaming:</span>
                    <span
                      className={
                        enableStreaming ? "text-green-400" : "text-gray-400"
                      }
                    >
                      {enableStreaming ? "Enabled" : "Disabled"}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Browser:</span>
                    <span
                      className={
                        window.MediaSource ? "text-green-400" : "text-red-400"
                      }
                    >
                      {window.MediaSource ? "Supported" : "Not Supported"}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
