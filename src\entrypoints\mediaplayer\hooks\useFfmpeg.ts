import { useRef, useState, useCallback, useEffect } from "react";
import { FFmpeg } from "@ffmpeg/ffmpeg";
import { FfmpegStatus, loadFfmpeg } from "@/lib/ffmpeg";
import { MediaFile } from "../MediaPlayerPage";
import { createMediaFile } from "../helper";
import { logger } from "@/lib/logger";

type FfmpegHookStatus =
  | "idle"
  | "loading"
  | "transcoding"
  | "error"
  | "cancelled";

interface UseFfmpegReturn {
  status: FfmpegHookStatus;
  progress: number | null;
  loadFfmpeg: () => Promise<void>;
  processFile: (file: File | string) => Promise<MediaFile | undefined>;
  cancelOperation: () => void;
  isLoaded: boolean;
}

export function useFfmpeg(): UseFfmpegReturn {
  const ffmpegRef = useRef<FFmpeg>(new FFmpeg());
  const [status, setStatus] = useState<FfmpegHookStatus>("idle");
  const [progress, setProgress] = useState<number | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Handle status changes from FFmpeg operations
  const handleStatusChange = useCallback((ffmpegStatus: FfmpegStatus) => {
    setStatus(ffmpegStatus.status);
    setProgress(ffmpegStatus.progress);
  }, []);

  // Handle progress updates from FFmpeg
  const handleFFmpegProgress = useCallback(
    (progressValue: number) => {
      const correctProgress =
        progressValue >= 100 && progress === null ? 0 : progressValue;
      setProgress(correctProgress);
    },
    [progress],
  );

  // Load FFmpeg
  const loadFfmpegInstance = useCallback(async () => {
    if (ffmpegRef.current.loaded) {
      setIsLoaded(true);
      return;
    }

    try {
      setStatus("loading");
      setProgress(null);

      await loadFfmpeg({ ffmpeg: ffmpegRef.current });

      setIsLoaded(true);
      setStatus("idle");
      setProgress(null);
    } catch (error) {
      logger.error("Failed to load FFmpeg:", error);
      setStatus("error");
      setProgress(null);
      throw error;
    }
  }, []);

  // Cancel any ongoing operation
  const cancelOperation = useCallback(() => {
    try {
      // Cancel any ongoing fetch operations
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
        abortControllerRef.current = null;
      }

      // Terminate FFmpeg if it's running
      if (ffmpegRef.current.loaded) {
        ffmpegRef.current.terminate();
      }

      setStatus("cancelled");
      setProgress(null);
    } catch (error) {
      logger.error("Error during cancellation:", error);
      setStatus("error");
    }
  }, []);

  // Process a file (either local File or URL string)
  const processFile = useCallback(
    async (file: File | string): Promise<MediaFile | undefined> => {
      try {
        // Create new abort controller for this operation
        abortControllerRef.current = new AbortController();

        setStatus("loading");
        setProgress(0);

        const mediaFile = await createMediaFile({
          ffmpeg: ffmpegRef.current,
          file,
          onStatusChange: handleStatusChange,
          abortSignal: abortControllerRef.current.signal,
        });

        if (mediaFile) {
          setStatus("idle");
          setProgress(null);

          // Clear progress after a short delay
          setTimeout(() => {
            setProgress(null);
          }, 1000);
        }

        return mediaFile;
      } catch (error) {
        if (abortControllerRef.current?.signal.aborted) {
          setStatus("cancelled");
        } else {
          logger.error("Error processing file:", error);
          setStatus("error");
        }
        setProgress(null);
        throw error;
      } finally {
        abortControllerRef.current = null;
      }
    },
    [handleStatusChange],
  );

  // Setup FFmpeg event listeners
  useEffect(() => {
    const ffmpeg = ffmpegRef.current;

    const handleLog = ({ message }: { message: string }) => {
      console.log("FFmpeg log:", message);
    };

    const handleProgress = ({
      progress: progressValue,
    }: {
      progress: number;
    }) => {
      const correctProgress = progressValue > 1 ? 1 : progressValue;
      handleFFmpegProgress(Math.round(correctProgress * 100));
    };

    ffmpeg.on("log", handleLog);
    ffmpeg.on("progress", handleProgress);

    // Auto-load FFmpeg on mount
    loadFfmpegInstance().catch((error: unknown) => {
      logger.error("Failed to auto-load FFmpeg:", error);
    });

    return () => {
      ffmpeg.off("log", handleLog);
      ffmpeg.off("progress", handleProgress);

      // Cleanup on unmount
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [loadFfmpegInstance, handleFFmpegProgress]);

  return {
    status,
    progress,
    loadFfmpeg: loadFfmpegInstance,
    processFile,
    cancelOperation,
    isLoaded,
  };
}
