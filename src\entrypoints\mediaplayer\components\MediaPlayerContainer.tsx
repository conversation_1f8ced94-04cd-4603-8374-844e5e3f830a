import {
  isDASHProvider,
  isHLSProvider,
  MediaPlayer,
  MediaProvider,
  MediaProviderAdapter,
} from "@vidstack/react";
import { MediaFile } from "../MediaPlayerPage";
import { AudioPlayer } from "../players/AudioPlayer";
import { VideoPlayer } from "../players/VideoPlayer";
import { getFileNameWithoutExtension } from "../helper";

type Prop = {
  currentMedia: MediaFile;
};

export const MediaPlayerContainer = ({ currentMedia }: Prop) => {
  function onProviderChange(provider: MediaProviderAdapter | null) {
    if (isHLSProvider(provider)) {
      provider.library = () => import("hls.js");
      return;
    }

    if (isDASHProvider(provider)) {
      provider.library = () => import("dashjs");
      return;
    }
  }
  console.log(currentMedia);

  return (
    <div className="w-full h-full">
      <MediaPlayer
        title={getFileNameWithoutExtension(currentMedia.name)}
        className="w-full h-full"
        src={currentMedia.src}
        autoPlay
        onProviderChange={onProviderChange}
      >
        <MediaProvider />
        {currentMedia.type === "video" ? (
          <VideoPlayer currentMedia={currentMedia} />
        ) : (
          <AudioPlayer currentMedia={currentMedia} />
        )}
      </MediaPlayer>
    </div>
  );
};
