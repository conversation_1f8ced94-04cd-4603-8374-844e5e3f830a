import { Music, Plus } from "lucide-react";

interface HeaderProps {
  onLoadMediaClick?: () => void;
}

export const Header = ({ onLoadMediaClick }: HeaderProps) => {
  return (
    <div className="bg-slate-900/80 backdrop-blur-sm px-6 py-4 border-b border-slate-800">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Music size={20} className="text-white" />
          <h1 className="text-xl font-bold text-white">MediaPlayer</h1>
        </div>

        {onLoadMediaClick && (
          <button
            onClick={onLoadMediaClick}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200 font-medium"
            title="Load New Media"
          >
            <Plus size={18} />
            Load Media
          </button>
        )}
      </div>
    </div>
  );
};
