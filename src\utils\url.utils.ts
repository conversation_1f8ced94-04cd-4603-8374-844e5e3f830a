import { isFormatSupported } from "@/entrypoints/mediaplayer/helper";
import { logger } from "@/lib/logger";

export function getFileNameFromUrl(url: string) {
  return url.split("/").pop();
}
export function isValidUrl(url: string): { isValid: boolean; reason?: string } {
  let parsed: URL | undefined;

  try {
    parsed = new URL(url);

    if (!["http:", "https:"].includes(parsed.protocol)) {
      return {
        isValid: false,
        reason: "Unsupported protocol (use HTTP/HTTPS)",
      };
    }

    return { isValid: true };
  } catch (e: unknown) {
    logger.error(e);

    const reason = "Malformed or invalid URL";

    return { isValid: false, reason };
  }
}

export const isSupportedMediaUrl = (url: string) => {
  return isValidUrl(url).isValid && mediaUrlHasSupportedFormatExtension(url);
};
export function mediaUrlHasSupportedFormatExtension(url: string) {
  const format = getFileFormatFromUrl(url);
  return format && isFormatSupported(format);
}
export const getFileFormatFromUrl = (fileName: string): string | undefined => {
  return fileName.split(".").pop()?.toLowerCase();
};

export function removeMediaSrcFromUrl() {
  const url = new URL(window.location.href);
  if (!url.searchParams.has("src")) return;
  url.searchParams.delete("src");
  window.history.replaceState({}, document.title, url.toString());
}
