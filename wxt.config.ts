import { build, defineConfig } from "wxt";
import { viteStaticCopy } from "vite-plugin-static-copy";

export default defineConfig({
  modules: ["@wxt-dev/module-react"],
  srcDir: "src",
  vite: () => ({
    plugins: [
      viteStaticCopy({
        targets: [
          {
            src: "node_modules/@ffmpeg/ffmpeg/dist/esm/*.js",
            dest: ".",
          },
        ],
      }),
    ] as any,
    optimizeDeps: {
      exclude: ["@ffmpeg/ffmpeg", "@ffmpeg/util", "dashjs"],
    },
  }),
  manifest: {
    permissions: ["storage", "tabs", "webNavigation", "downloads"],
    action: {},
    host_permissions: ["*://*/*"],
    content_security_policy: {
      extension_pages:
        "script-src 'self' 'wasm-unsafe-eval'; object-src 'self';",
      sandbox:
        "sandbox allow-scripts allow-forms allow-popups allow-modals; script-src 'self' 'unsafe-inline' 'unsafe-eval' 'wasm-unsafe-eval'; child-src 'self';",
    },
  },
});
