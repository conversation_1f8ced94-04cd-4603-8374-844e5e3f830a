import { logger } from "@/lib/logger";
import { isSupportedMediaUrl } from "@/utils/url.utils";

export default defineBackground(() => {
  main();
  openSupportedMediaUrlsInCustomTab();
});

function main() {
  browser.action.onClicked.addListener(openPlayerTabOnActionClick);
}

async function createNewTab(url: string): Promise<void> {
  try {
    await browser.tabs.create({ url });
  } catch (error: unknown) {
    logger.error("Failed to create new tab", error);
    throw error;
  }
}

function openPlayerTabOnActionClick(): void {
  const playerFileUrl = browser.runtime.getURL("/mediaplayer.html");
  createNewTab(playerFileUrl).catch(logger.error);
}

function openSupportedMediaUrlsInCustomTab() {
  browser.webNavigation.onBeforeNavigate.addListener((details) => {
    console.log("onBeforeNavigate", details);
    if (details.frameId !== 0) return; // only main frame
    if (isSupportedMediaUrl(details.url)) {
      logger.info("Opening supported media url in custom tab", details.url);
      openMediaPlayerInCurrentTab(details.tabId, details.url);
    }
  });
}

function openMediaPlayerInCurrentTab(tabId: number, mediaUrl: string) {
  browser.tabs.update(tabId, {
    url: browser.runtime.getURL(
      `/mediaplayer.html?src=${encodeURIComponent(mediaUrl)}`,
    ),
  });
}
