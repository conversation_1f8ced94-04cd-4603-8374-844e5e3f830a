import React from "react";
import "@vidstack/react/player/styles/default/theme.css";
import "@vidstack/react/player/styles/default/layouts/video.css";
import "@vidstack/react/player/styles/default/layouts/audio.css";
import { MediaFile } from "../MediaPlayerPage";
import { StreamingMediaFile } from "../helper";
import { UnifiedMediaPlayerContainer } from "../components/UnifiedMediaPlayerContainer";

type Prop = {
  currentMedia: MediaFile | StreamingMediaFile;
};

export const PlayerTab = ({ currentMedia }: Prop) => {
  return (
    <div className="h-full">
      <UnifiedMediaPlayerContainer currentMedia={currentMedia} />
    </div>
  );
};
