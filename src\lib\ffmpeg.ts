/* eslint-disable @typescript-eslint/no-unnecessary-condition */
import { fetchFile, toBlobURL } from "@ffmpeg/util";
import { FFmpeg } from "@ffmpeg/ffmpeg";
import { logger } from "./logger";

// ------------------------ Interfaces & Types ------------------------

export interface CodecInfo {
  hasVideo: boolean;
  hasAudio: boolean;
  videoCodec?: string;
  audioCodec?: string;
}

interface TranscodeStrategyParams {
  codecInfo: CodecInfo;
  inputFileName: string;
  outputFileName: string;
  outputFormat: OutputFileFormat;
}

export type OutputFileFormat = "mp4" | "mp3";

interface TranscodeArgs {
  ffmpeg: FFmpeg;
  file: string | File;
  inputFileName: string;
  outputFileFormat: OutputFileFormat;
  outputMimeType: string;
  onStatusChange: (status: FfmpegStatus) => void;
  abortSignal?: AbortSignal;
}
export interface FfmpegStatus {
  progress: number | null;
  status: "loading" | "transcoding" | "idle";
}

interface LoadFfmpegArgs {
  ffmpeg: FFmpeg;
}

export async function loadFfmpeg({ ffmpeg }: LoadFfmpegArgs) {
  if (ffmpeg.loaded) {
    console.log("Already ffmpeg loaded");
    return;
  }
  console.log("Loading ffmpeg.....");

  try {
    await ffmpeg.load({
      coreURL: browser.runtime.getURL("/lib/ffmpeg-core.js"),
      wasmURL: browser.runtime.getURL("/lib/ffmpeg-core.wasm"),
      workerURL: browser.runtime.getURL("/lib/ffmpeg-core.worker.js"),
    });
    console.log("FFmpeg loaded");
  } catch (err) {
    console.error("Failed to load FFmpeg:", err);
    throw err;
  }
}

export async function transcodeFile({
  ffmpeg,
  file,
  inputFileName,
  outputFileFormat,
  onStatusChange,
  outputMimeType,
  abortSignal,
}: TranscodeArgs): Promise<string> {
  if (!ffmpeg.loaded) {
    throw new Error("FFmpeg is not loaded.");
  }

  try {
    const outputFileName = `output.${outputFileFormat}`;

    await loadInputFile({
      ffmpeg,
      file,
      inputFileName,
      onStatusChange,
      abortSignal,
    });

    const codecInfo = await extractCodecInfo(ffmpeg, inputFileName);

    const ffmpegArgs = determineTranscodeStrategy({
      codecInfo,
      inputFileName,
      outputFileName,
      outputFormat: outputFileFormat,
    });

    onStatusChange({ progress: 0, status: "transcoding" });
    updateStatusOnTranscodingProgress(ffmpeg, onStatusChange);
    await ffmpeg.exec(ffmpegArgs);
    return await getOutputURL(ffmpeg, outputFileName, outputMimeType);
  } catch (error) {
    console.error("Failed to transcode file: ", error);
    throw error;
  } finally {
    onStatusChange({ progress: 0, status: "idle" });
  }
}

// ------------------------ Helper Functions ------------------------
function updateStatusOnTranscodingProgress(
  ffmpeg: FFmpeg,
  onStatusChange: (status: FfmpegStatus) => void
) {
  ffmpeg.on("progress", ({ progress }) => {
    const correctProgress = (progress > 1 ? 1 : progress) * 100;
    onStatusChange({ progress: correctProgress, status: "transcoding" });
  });
}

type LoadInputFileArgs = {
  ffmpeg: FFmpeg;
  file: string | File;
  inputFileName: string;
  onStatusChange: (status: FfmpegStatus) => void;
  abortSignal?: AbortSignal;
};
async function loadInputFile({
  ffmpeg,
  file,
  inputFileName,
  onStatusChange,
  abortSignal,
}: LoadInputFileArgs): Promise<void> {
  onStatusChange({ progress: 0, status: "loading" });
  if (typeof file === "string") {
    await loadFileWithProgress({
      url: file,
      ffmpeg,
      inputFileName,
      abortSignal,
      onProgress: (progress) => {
        console.log("Loading progress", progress);
        onStatusChange({ progress, status: "loading" });
      },
    });
    onStatusChange({ progress: 100, status: "loading" });
    return;
  }
  const data = await fetchFile(file);
  await ffmpeg.writeFile(inputFileName, data);
  onStatusChange({ progress: 100, status: "loading" });
}

async function extractCodecInfo(
  ffmpeg: FFmpeg,
  inputFileName: string
): Promise<CodecInfo> {
  const stderrLines: string[] = [];

  ffmpeg.on("log", ({ message }) => {
    stderrLines.push(message);
  });

  try {
    await ffmpeg.exec(["-i", inputFileName]);
  } catch {
    // We expect ffmpeg to error when probing, ignore
  }

  console.log("stderrLines", stderrLines);

  const output = stderrLines.join("\n");
  const hasVideo = /Stream #\d+:\d+.*Video:/i.test(output);
  const hasAudio = /Stream #\d+:\d+.*Audio:/i.test(output);

  const videoCodec = /Video:\s?([a-zA-Z0-9]+)/.exec(output)?.[1];
  const audioCodec = /Audio:\s?([a-zA-Z0-9]+)/.exec(output)?.[1];

  return { hasVideo, hasAudio, videoCodec, audioCodec };
}

// Add this simple function to your existing code
function canRemuxToMP4(videoCodec: string, audioCodec: string) {
  const mp4VideoCodecs = [
    "h264",
    "avc1",
    "h265",
    "hevc",
    "av01",
    "mp4v",
    "mpeg4",
  ];
  const mp4AudioCodecs = ["aac", "mp3", "ac3", "mp4a"];

  const videoOK = videoCodec
    ? mp4VideoCodecs.includes(videoCodec.toLowerCase())
    : true;
  const audioOK = audioCodec
    ? mp4AudioCodecs.includes(audioCodec.toLowerCase())
    : true;

  return videoOK && audioOK;
}

// Replace your existing determineTranscodeStrategy function with this improved version
function determineTranscodeStrategy({
  codecInfo,
  inputFileName,
  outputFileName,
  outputFormat,
}: TranscodeStrategyParams): string[] {
  const { hasVideo, hasAudio, videoCodec, audioCodec } = codecInfo;

  if (hasVideo && outputFormat === "mp4") {
    // Use the simple compatibility check
    if (canRemuxToMP4(videoCodec, audioCodec)) {
      console.log(`Can remux: ${videoCodec}/${audioCodec} -> MP4`);
      return [
        "-i",
        inputFileName,
        "-c",
        "copy",
        "-movflags",
        "+faststart",
        outputFileName,
      ];
    }

    console.log(`Need re-encode: ${videoCodec}/${audioCodec} -> MP4`);
    return [
      "-i",
      inputFileName,
      "-c:v",
      "libx264",
      "-c:a",
      "aac",
      "-movflags",
      "+faststart",
      outputFileName,
    ];
  }

  if (!hasVideo && hasAudio && outputFormat === "mp3") {
    if (audioCodec === "mp3") {
      console.log("Can copy MP3 audio");
      return ["-i", inputFileName, "-c", "copy", outputFileName];
    }

    console.log(`Need re-encode: ${audioCodec} -> MP3`);
    return ["-i", inputFileName, "-c:a", "libmp3lame", outputFileName];
  }

  throw new Error("Unsupported codec or format combination.");
}
async function getOutputURL(
  ffmpeg: FFmpeg,
  outputFileName: string,
  outputMimeType: string
): Promise<string> {
  const data = (await ffmpeg.readFile(outputFileName)) as {
    buffer: ArrayBuffer;
  };
  return URL.createObjectURL(new Blob([data.buffer], { type: outputMimeType }));
}

export function convertToBlobUrl(
  url: string,
  mimeType: string
): Promise<string> {
  try {
    return toBlobURL(url, mimeType);
  } catch (error: unknown) {
    logger.error("Failed to convert to blob url: ", error);
    throw error;
  }
}

type LoadFileWithProgressArgs = {
  url: string;
  ffmpeg: FFmpeg;
  inputFileName: string;
  onProgress: (progress: number) => void;
  abortSignal?: AbortSignal;
};
async function loadFileWithProgress({
  url,
  ffmpeg,
  inputFileName,
  onProgress,
  abortSignal,
}: LoadFileWithProgressArgs) {
  const res = await fetch(url, { signal: abortSignal });
  const contentLength = parseInt(res.headers.get("Content-Length") ?? "0", 10);

  let received = 0;
  const chunks: Uint8Array[] = [];

  const resBody = res.body;
  if (!resBody) {
    throw new Error("No response body");
  }
  const reader = resBody.getReader();

  while (true) {
    const { done, value } = await reader.read();
    if (done || abortSignal?.aborted) break;
    if (value) {
      chunks.push(value);
      received += value.length;
      const progress = (received / contentLength) * 100;
      onProgress(progress);
    }
  }

  const fileData = new Uint8Array(received);
  let offset = 0;
  for (const chunk of chunks) {
    fileData.set(chunk, offset);
    offset += chunk.length;
  }

  await ffmpeg.writeFile(inputFileName, fileData);
}
