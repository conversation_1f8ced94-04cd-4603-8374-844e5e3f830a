import React from "react";
import "@vidstack/react/player/styles/default/theme.css";
import "@vidstack/react/player/styles/default/layouts/video.css";
import "@vidstack/react/player/styles/default/layouts/audio.css";
import { MediaFile } from "../MediaPlayerPage";
import { MediaPlayerContainer } from "../components/MediaPlayerContainer";

type Prop = {
  currentMedia: MediaFile;
};

export const PlayerTab = ({ currentMedia }: Prop) => {
  return (
    <div className="h-full">
      <MediaPlayerContainer currentMedia={currentMedia} />
    </div>
  );
};
