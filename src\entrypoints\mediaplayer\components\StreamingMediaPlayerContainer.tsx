import React, { useEffect, useRef, useState, useCallback } from "react";
import {
  isDASHProvider,
  isHLSProvider,
  MediaPlayer,
  MediaProvider,
  MediaProviderAdapter,
} from "@vidstack/react";
import { MediaFile } from "../MediaPlayerPage";
import { AudioPlayer } from "../players/AudioPlayer";
import { VideoPlayer } from "../players/VideoPlayer";
import { getFileNameWithoutExtension } from "../helper";
import {
  MediaSourceManager,
  getBestSupportedMimeType,
} from "@/lib/mediaSource";
import { transcodeFileInChunks, loadFfmpeg } from "@/lib/ffmpeg";
import { FFmpeg } from "@ffmpeg/ffmpeg";
import { logger } from "@/lib/logger";

interface StreamingMediaPlayerContainerProps {
  file: File | string;
  fileName: string;
  mediaType: "video" | "audio";
  ffmpeg: FFmpeg;
  onError?: (error: Error) => void;
  onProgress?: (progress: number) => void;
  onStreamingReady?: () => void;
  onFallback?: (fallbackMedia: MediaFile) => void;
  onFallbackFailed?: (error: Error) => void;
}

export const StreamingMediaPlayerContainer = ({
  file,
  fileName,
  mediaType,
  ffmpeg,
  onError,
  onProgress,
  onStreamingReady,
  onFallback,
  onFallbackFailed,
}: StreamingMediaPlayerContainerProps) => {
  const [streamingMedia, setStreamingMedia] = useState<MediaFile | null>(null);
  const [isInitializing, setIsInitializing] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const mediaSourceManagerRef = useRef<MediaSourceManager | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  const MAX_RETRIES = 2;

  const handleError = useCallback(
    (error: Error, canRetry: boolean = false) => {
      logger.error("Streaming player error:", error);

      if (canRetry && retryCount < MAX_RETRIES) {
        logger.info(
          `Retrying streaming (attempt ${retryCount + 1}/${MAX_RETRIES})`
        );
        setRetryCount((prev) => prev + 1);
        setError(null);

        // Retry after a short delay
        setTimeout(() => {
          initializeStreaming();
        }, 1000);

        return;
      }

      // If we can't retry or have exceeded max retries, show error and trigger fallback
      setError(error.message);
      setIsInitializing(false);
      onError?.(error);
    },
    [onError, retryCount, MAX_RETRIES]
  );

  const initializeStreaming = useCallback(async () => {
    try {
      setIsInitializing(true);

      // Only support video streaming for now
      if (mediaType !== "video") {
        throw new Error(
          "Streaming is currently only supported for video files"
        );
      }

      // Ensure FFmpeg is loaded
      if (!ffmpeg.loaded) {
        await loadFfmpeg({ ffmpeg });
      }

      // Get the best supported MIME type for fragmented MP4
      const mimeType = getBestSupportedMimeType(true, true); // Assume video with audio

      // Create MediaSource manager
      const mediaSourceManager = new MediaSourceManager({
        mimeType,
        onSourceOpen: () => {
          logger.info("MediaSource opened, starting transcoding");
          startChunkedTranscoding();
        },
        onError: handleError,
        onUpdateEnd: () => {
          // Could add buffer management logic here
        },
      });

      mediaSourceManagerRef.current = mediaSourceManager;

      // Create MediaFile object with MediaSource URL
      const streamingMediaFile: MediaFile = {
        name: fileName,
        src: {
          src: mediaSourceManager.getObjectUrl(),
          type: "video/mp4", // Vidstack expects this format
        },
        type: mediaType,
        format: "mp4",
        size: file instanceof File ? file.size : undefined,
      };

      setStreamingMedia(streamingMediaFile);
      setIsInitializing(false);
      onStreamingReady?.();
    } catch (error) {
      setIsInitializing(false);
      handleError(error as Error);
    }
  }, [file, fileName, mediaType, ffmpeg, handleError, onStreamingReady]);

  const startChunkedTranscoding = useCallback(async () => {
    if (!mediaSourceManagerRef.current) {
      return;
    }

    try {
      // Create abort controller for this transcoding session
      abortControllerRef.current = new AbortController();

      await transcodeFileInChunks({
        ffmpeg,
        file,
        inputFileName: fileName,
        outputMimeType: "video/mp4",
        chunkDurationSeconds: 10, // 10-second chunks
        onStatusChange: (status) => {
          if (status.progress !== null) {
            onProgress?.(status.progress);
          }
        },
        onChunkReady: (chunk, chunkIndex, isLast) => {
          logger.info(
            `Chunk ${chunkIndex} ready, size: ${chunk.length}, isLast: ${isLast}`
          );
          mediaSourceManagerRef.current?.appendChunk(chunk, chunkIndex, isLast);
        },
        abortSignal: abortControllerRef.current.signal,
      });

      logger.info("Chunked transcoding completed");
    } catch (error) {
      if (error instanceof Error && error.message.includes("aborted")) {
        logger.info("Transcoding was aborted");
        return;
      }
      handleError(error as Error);
    }
  }, [ffmpeg, file, fileName, onProgress, handleError]);

  const handleProviderChange = useCallback(
    (provider: MediaProviderAdapter | null) => {
      if (isHLSProvider(provider)) {
        provider.library = () => import("hls.js");
        return;
      }

      if (isDASHProvider(provider)) {
        provider.library = () => import("dashjs");
        return;
      }
    },
    []
  );

  // Initialize streaming when component mounts
  useEffect(() => {
    initializeStreaming();

    // Cleanup function
    return () => {
      // Abort any ongoing transcoding
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      // Cleanup MediaSource
      if (mediaSourceManagerRef.current) {
        mediaSourceManagerRef.current.dispose();
        mediaSourceManagerRef.current = null;
      }
    };
  }, [initializeStreaming]);

  // Show error state if streaming failed
  if (error && !isInitializing) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <div className="text-center max-w-md mx-4">
          <div className="text-red-400 text-6xl mb-4">⚠️</div>
          <h3 className="text-xl font-semibold text-white mb-2">
            Streaming Failed
          </h3>
          <p className="text-gray-300 mb-4">{error}</p>
          <div className="space-y-2">
            <p className="text-sm text-gray-400">
              {retryCount < MAX_RETRIES
                ? `Retrying... (${retryCount}/${MAX_RETRIES})`
                : "Falling back to regular transcoding..."}
            </p>
            <button
              onClick={() => {
                setError(null);
                setRetryCount(0);
                initializeStreaming();
              }}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Show loading state while initializing
  if (isInitializing || !streamingMedia) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
          <p className="text-white">
            {retryCount > 0
              ? `Retrying streaming... (${retryCount}/${MAX_RETRIES})`
              : "Initializing streaming..."}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full">
      <MediaPlayer
        title={getFileNameWithoutExtension(streamingMedia.name)}
        className="w-full h-full"
        src={streamingMedia.src}
        autoPlay
        onProviderChange={handleProviderChange}
      >
        <MediaProvider />
        {streamingMedia.type === "video" ? (
          <VideoPlayer currentMedia={streamingMedia} />
        ) : (
          <AudioPlayer currentMedia={streamingMedia} />
        )}
      </MediaPlayer>
    </div>
  );
};

/**
 * Hook to check if streaming is supported for a given file
 */
export const useStreamingSupport = (
  file: File | string,
  mediaType: "video" | "audio"
) => {
  const [isSupported, setIsSupported] = useState(false);
  const [reason, setReason] = useState<string>("");

  useEffect(() => {
    // Check MediaSource support
    if (!window.MediaSource) {
      setIsSupported(false);
      setReason("MediaSource API not supported");
      return;
    }

    // Currently only support video streaming
    if (mediaType !== "video") {
      setIsSupported(false);
      setReason("Streaming currently only supports video files");
      return;
    }

    // Check if we have any supported fragmented MP4 MIME types
    try {
      getBestSupportedMimeType(true, true);
      setIsSupported(true);
      setReason("");
    } catch (error) {
      setIsSupported(false);
      setReason("No supported fragmented MP4 codecs");
    }
  }, [file, mediaType]);

  return { isSupported, reason };
};
