import React from "react";
import { useMediaState, useMediaRemote, formatTime } from "@vidstack/react";
import {
  Music,
  PlayIcon,
  PauseIcon,
  SkipForward,
  SkipBack,
  Repeat,
  Volume2,
  VolumeX,
} from "lucide-react";
import { MediaFile } from "../MediaPlayerPage";
import * as Slider from "../components/vidstack/sliders";
import { AudioMenu } from "../components/vidstack/AudioMenu";
import { getFileNameWithoutExtension } from "../helper";

interface AudioPlayerProps {
  currentMedia: MediaFile;
}

export const AudioPlayer = ({ currentMedia }: AudioPlayerProps) => {
  const remote = useMediaRemote();

  // Media states
  const isPaused = useMediaState("paused");
  const currentTime = useMediaState("currentTime");
  const duration = useMediaState("duration");

  const muted = useMediaState("muted");
  const loop = useMediaState("loop");

  const handleSkipBackward = () => {
    const newTime = Math.max(0, currentTime - 10);
    remote.seek(newTime);
  };

  const handleSkipForward = () => {
    const newTime = Math.min(duration, currentTime + 10);
    remote.seek(newTime);
  };

  const handlePlayPause = () => {
    if (isPaused) {
      remote.play();
    } else {
      remote.pause();
    }
  };

  const handleMute = () => {
    remote.toggleMuted();
  };

  const handleRepeat = () => {
    remote.userPrefersLoopChange(!loop);
  };

  return (
    <div className="h-full w-full bg-gradient-to-br from-slate-950 via-slate-900 to-slate-950 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-black/20"></div>
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(59,130,246,0.1)_0%,transparent_50%)]"></div>

      {/* Volume Controls - Positioned absolutely in top right */}
      <div className="absolute top-8 right-8 z-20 flex items-center space-x-3 bg-slate-800/50 backdrop-blur-sm rounded-full px-4 py-2 border border-slate-700/50">
        <button
          onClick={handleMute}
          className="p-1 rounded-full hover:bg-slate-700/50 transition-all duration-200"
          title={muted ? "Unmute" : "Mute"}
        >
          {muted ? (
            <VolumeX className="w-4 h-4 text-slate-400" />
          ) : (
            <Volume2 className="w-4 h-4 text-slate-300" />
          )}
        </button>
        <div className="w-20">
          <Slider.Volume />
        </div>
      </div>

      {/* Main Content Container */}
      <div className="relative z-10 h-full flex flex-col px-8 py-8 max-w-6xl mx-auto">
        {/* Vinyl Record Section - Main Focus */}
        <div className="flex-1 flex flex-col items-center justify-center">
          {/* Song Info Section */}
          <div className="text-center mb-6">
            <h1 className="text-2xl md:text-3xl font-bold text-white mb-2 tracking-tight">
              {getFileNameWithoutExtension(currentMedia.name)}
            </h1>
            <p className="text-lg text-slate-400 font-medium">Unknown Artist</p>
            <p className="text-sm text-slate-500 mt-1 capitalize">
              {currentMedia.format} Audio
            </p>
          </div>

          {/* Vinyl Record */}
          <div className="flex items-center justify-center mb-6">
            {/* Glow Effect */}
            <div className="absolute w-80 h-80 md:w-96 md:h-96 rounded-full bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-pink-500/20 blur-3xl animate-pulse"></div>

            {/* Vinyl Record */}
            <div
              className={`relative w-72 h-72 md:w-80 md:h-80 transition-all duration-500 ${
                !isPaused ? "animate-spin" : ""
              }`}
              style={{
                animationDuration: "8s",
                filter: "drop-shadow(0 25px 50px rgba(0, 0, 0, 0.5))",
              }}
            >
              {/* Outer Ring */}
              <div className="absolute inset-0 rounded-full bg-gradient-to-br from-slate-800 via-slate-900 to-black border-4 border-slate-700 shadow-2xl">
                {/* Record Grooves - Multiple concentric circles */}
                <div className="absolute inset-6 rounded-full border border-slate-600/50"></div>
                <div className="absolute inset-8 rounded-full border border-slate-600/40"></div>
                <div className="absolute inset-10 rounded-full border border-slate-600/30"></div>
                <div className="absolute inset-12 rounded-full border border-slate-600/25"></div>
                <div className="absolute inset-14 rounded-full border border-slate-600/20"></div>
                <div className="absolute inset-16 rounded-full border border-slate-600/15"></div>
                <div className="absolute inset-18 rounded-full border border-slate-600/10"></div>

                {/* Center Label */}
                <div className="absolute inset-24 md:inset-28 rounded-full bg-gradient-to-br from-amber-400 via-yellow-500 to-orange-500 flex items-center justify-center shadow-lg border-2 border-amber-300">
                  <div className="relative">
                    <Music className="w-12 h-12 md:w-16 md:h-16 text-black drop-shadow-sm" />
                    {/* Subtle inner glow */}
                    <div className="absolute inset-0 rounded-full bg-white/20 blur-sm"></div>
                  </div>
                </div>

                {/* Reflection effect */}
                <div className="absolute inset-0 rounded-full bg-gradient-to-tr from-white/10 via-transparent to-transparent"></div>
              </div>
            </div>
          </div>
        </div>

        {/* Controls Section */}
        <div className="w-full max-w-2xl space-y-6 mx-auto">
          {/* Progress Bar */}
          <div className="space-y-4">
            <div className="flex justify-between text-lg font-medium text-slate-300">
              <span>{formatTime(currentTime)}</span>
              <span>{formatTime(duration)}</span>
            </div>
            <div className="relative">
              <Slider.Time />
            </div>
          </div>

          {/* Main Control Buttons */}
          <div className="flex items-center justify-center space-x-6">
            {/* Repeat Button */}
            <button
              onClick={handleRepeat}
              className="p-3 rounded-full bg-slate-800/50 hover:bg-slate-700/50 transition-all duration-200 hover:scale-105"
              title="Toggle Repeat"
            >
              <Repeat
                className={`w-6 h-6 ${loop ? "text-blue-400" : "text-slate-300"}`}
              />
            </button>

            {/* Skip Backward Button */}
            <button
              onClick={handleSkipBackward}
              className="p-3 rounded-full bg-slate-800/50 hover:bg-slate-700/50 transition-all duration-200 hover:scale-105"
              title="Skip Backward 10s"
            >
              <SkipBack className="w-6 h-6 text-slate-300" />
            </button>

            {/* Large Play/Pause Button */}
            <button
              onClick={handlePlayPause}
              className="p-4 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
              title={isPaused ? "Play" : "Pause"}
            >
              {isPaused ? (
                <PlayIcon className="w-8 h-8 text-white ml-1" />
              ) : (
                <PauseIcon className="w-8 h-8 text-white" />
              )}
            </button>

            {/* Skip Forward Button */}
            <button
              onClick={handleSkipForward}
              className="p-3 rounded-full bg-slate-800/50 hover:bg-slate-700/50 transition-all duration-200 hover:scale-105"
              title="Skip Forward 10s"
            >
              <SkipForward className="w-6 h-6 text-slate-300" />
            </button>

            {/* Settings Menu */}
            <div className="relative">
              <AudioMenu />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
