import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Seek<PERSON>utt<PERSON>,
  useMediaRemote,
  useMediaState,
} from "@vidstack/react";
import {
  SeekForward10Icon,
  SeekBackward10Icon,
  VolumeHighIcon,
  MuteIcon,
  VolumeLowIcon,
  RepeatIcon,
} from "@vidstack/react/icons";
import { PlayIcon, PauseIcon } from "lucide-react";
import { ToolTip } from "./ToolTip";

type ButtonProps = {
  onClick: () => void;
};

const BUTTON_CLASSNAME =
  "ring-sky-400 relative inline-flex h-10 w-10 cursor-pointer items-center justify-center rounded-md outline-none ring-inset hover:bg-white/20 data-[focus]:ring-4 aria-hidden:hidden";

export const Play = () => {
  const isPaused = useMediaState("paused");
  return (
    <ToolTip content={isPaused ? "Play" : "Pause"}>
      <PlayButton className={`group ${BUTTON_CLASSNAME}`}>
        <PlayIcon className="w-8 h-8 hidden group-data-[paused]:block" />
        <PauseIcon className="w-8 h-8 group-data-[paused]:hidden" />
      </PlayButton>
    </ToolTip>
  );
};

export const SkipForward = ({ onClick }: ButtonProps) => {
  return (
    <SeekBtn onClick={onClick} seconds={10}>
      <SeekForward10Icon className="w-8 h-8" />
    </SeekBtn>
  );
};

export function SkipBackward({ onClick }: ButtonProps) {
  return (
    <SeekBtn onClick={onClick} seconds={-10}>
      <SeekBackward10Icon className="w-8 h-8" />
    </SeekBtn>
  );
}

type SeekButtonProps = {
  onClick: () => void;
  seconds: number;
  children: React.ReactNode;
};

function SeekBtn({ onClick, children }: SeekButtonProps) {
  return (
    <SeekButton className={BUTTON_CLASSNAME} onClick={onClick}>
      {children}
    </SeekButton>
  );
}

export const Repeat = () => {
  const loop = useMediaState("loop");
  const remote = useMediaRemote();

  return (
    <button
      className={BUTTON_CLASSNAME}
      onClick={() => {
        remote.userPrefersLoopChange(!loop);
      }}
    >
      <RepeatIcon className="w-8 h-8" color={loop ? "#00FFFF" : "#FFFFFF"} />
    </button>
  );
};

export const Mute = () => {
  return (
    <MuteButton className={`group ${BUTTON_CLASSNAME} mx-1`}>
      <MuteIcon className="w-8 h-8 hidden group-data-[state='muted']:block" />
      <VolumeLowIcon className="w-8 h-8 hidden group-data-[state='low']:block" />
      <VolumeHighIcon className="w-8 h-8 hidden group-data-[state='high']:block" />
    </MuteButton>
  );
};
