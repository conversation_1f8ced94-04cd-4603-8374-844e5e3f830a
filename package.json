{"name": "webplayer", "description": "A powerful Chrome extension with a built-in media player that supports playing both video and audio files from various sources and formats.", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "wxt", "dev:firefox": "wxt -b firefox", "build": "wxt build", "build:firefox": "wxt build -b firefox", "zip": "wxt zip", "zip:firefox": "wxt zip -b firefox", "compile": "tsc --noEmit", "postinstall": "wxt prepare", "lint": "eslint ./src --no-warn-ignored --max-warnings 0 --fix", "prepare": "husky"}, "lint-staged": {"*.{ts,tsx}": ["prettier --write", "eslint  --no-warn-ignored --max-warnings 0 --fix"], "*.md": ["prettier --write"]}, "dependencies": {"@ffmpeg/ffmpeg": "^0.12.15", "@ffmpeg/util": "^0.12.2", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/postcss": "^4.1.11", "@vidstack/react": "^1.12.13", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dashjs": "^5.0.3", "hls.js": "^1.6.8", "lucide-react": "^0.525.0", "media-icons": "^1.1.5", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hot-toast": "^2.5.2", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.5", "vite-plugin-static-copy": "^3.1.1", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/js": "^9.30.1", "@ffmpeg/types": "^0.12.4", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.3", "@types/webtorrent": "^0.110.0", "@wxt-dev/module-react": "^1.1.3", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^16.3.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.13", "tailwindcss": "^4.1.11", "typescript": "^5.8.3", "typescript-eslint": "^8.35.1", "vitest": "^3.1.3", "wxt": "^0.20.6"}}