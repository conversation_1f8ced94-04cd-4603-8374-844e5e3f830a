import React from "react";
import { FfmpegStatus, loadFfmpeg, transcodeFile } from "@/lib/ffmpeg";
import { notify } from "@/lib/notify";
import { FFmpeg } from "@ffmpeg/ffmpeg";
import { MediaFile } from "./MediaPlayerPage";
import { AudioMimeType, HLSMimeType, VideoMimeType } from "@vidstack/react";
import {
  getFileFormatFromUrl,
  getFileNameFromUrl,
  mediaUrlHasSupportedFormatExtension,
} from "@/utils/url.utils";

type AudioFileFormat =
  | "mp3"
  | "wav"
  | "flac"
  | "aac"
  | "m4a"
  | "opus"
  | "alac"
  | "ape";
type VideoFileFormat =
  | "mp4"
  | "m3u8"
  | "wmv"
  | "mpd"
  | "webm"
  | "mkv"
  | "flv"
  | "avi"
  | "ts"
  | "mov"
  | "ogg";
type TorrentFileFormat = "torrent";

export type SupportedFileFormat =
  | AudioFileFormat
  | VideoFileFormat
  | TorrentFileFormat;

export type OutputFileFormat = "mp4" | "mp3";
interface NativeFormatInfo {
  name: SupportedFileFormat;
  mimeType: string;
  isNative: boolean;
  type: "video" | "audio" | "torrent";
  requiredConversion: false;
}

interface ConversionFormatInfo {
  name: SupportedFileFormat;
  mimeType: string;
  isNative: boolean;
  requiredConversion: true;
  type: "video" | "audio" | "torrent";
  conversionFormat: OutputFileFormat;
  conversionMimeType: "video/mp4" | "audio/mpeg";
}

export type PlayerSupportedMimeType =
  | AudioMimeType
  | VideoMimeType
  | HLSMimeType
  | "application/dash+xml"
  | "video/youtube"
  | "video/vimeo";
export type FormatInfo = NativeFormatInfo | ConversionFormatInfo;

export const SUPPORTED_VIDEO_FORMATS: Record<VideoFileFormat, FormatInfo> = {
  mp4: {
    name: "mp4",
    mimeType: "video/mp4",
    isNative: true,
    requiredConversion: false,
    type: "video",
  },
  m3u8: {
    name: "m3u8",
    mimeType: "application/vnd.apple.mpegurl",
    isNative: false,
    requiredConversion: false,
    type: "video",
  },
  mpd: {
    name: "mpd",
    mimeType: "application/dash+xml",
    isNative: false,
    requiredConversion: false,
    type: "video",
  },
  webm: {
    name: "webm",
    mimeType: "video/webm",
    isNative: true,
    requiredConversion: false,
    type: "video",
  },
  wmv: {
    name: "wmv",
    mimeType: "video/x-ms-wmv",
    isNative: false,
    requiredConversion: true,
    conversionFormat: "mp4",
    conversionMimeType: "video/mp4",
    type: "video",
  },
  mkv: {
    name: "mkv",
    mimeType: "video/x-matroska",
    isNative: false,
    requiredConversion: true,
    conversionFormat: "mp4",
    conversionMimeType: "video/mp4",
    type: "video",
  },
  flv: {
    name: "flv",
    mimeType: "video/x-flv",
    isNative: false,
    requiredConversion: true,
    conversionFormat: "mp4",
    conversionMimeType: "video/mp4",
    type: "video",
  },
  avi: {
    name: "avi",
    mimeType: "video/avi",
    isNative: false,
    requiredConversion: true,
    conversionFormat: "mp4",
    conversionMimeType: "video/mp4",
    type: "video",
  },
  ts: {
    name: "ts",
    mimeType: "video/mp2t",
    isNative: false,
    requiredConversion: true,
    conversionFormat: "mp4",
    conversionMimeType: "video/mp4",
    type: "video",
  },
  mov: {
    name: "mov",
    mimeType: "video/quicktime",
    isNative: false,
    requiredConversion: true,
    conversionFormat: "mp4",
    conversionMimeType: "video/mp4",
    type: "video",
  },

  ogg: {
    name: "ogg",
    mimeType: "video/ogg",
    isNative: true,
    type: "video",
    requiredConversion: false,
  },
} as const;

export type VidStackSupportedMimeType =
  | AudioMimeType
  | VideoMimeType
  | HLSMimeType
  | "application/dash+xml"
  | "video/youtube"
  | "video/vimeo";

export const TORRENT_FORMAT: Record<TorrentFileFormat, FormatInfo> = {
  torrent: {
    name: "torrent",
    mimeType: "application/x-bittorrent",
    isNative: false,
    requiredConversion: false,
    type: "torrent",
  },
};
export const SUPPORTED_AUDIO_FORMATS: Record<AudioFileFormat, FormatInfo> = {
  mp3: {
    name: "mp3",
    mimeType: "audio/mpeg",
    isNative: true,
    type: "audio",
    requiredConversion: false,
  },
  wav: {
    name: "wav",
    mimeType: "audio/wav",
    isNative: true,
    type: "audio",
    requiredConversion: false,
  },
  flac: {
    name: "flac",
    mimeType: "audio/flac",
    isNative: true,
    type: "audio",
    requiredConversion: false,
  },
  aac: {
    name: "aac",
    mimeType: "audio/aac",
    isNative: true,
    type: "audio",
    requiredConversion: false,
  },
  m4a: {
    name: "m4a",
    mimeType: "audio/mp4",
    isNative: true,
    type: "audio",
    requiredConversion: false,
  },
  opus: {
    name: "opus",
    mimeType: "audio/ogg; codecs=opus",
    isNative: true,
    type: "audio",
    requiredConversion: false,
  },
  alac: {
    name: "alac",
    mimeType: "audio/alac",
    isNative: false,
    type: "audio",
    requiredConversion: true,
    conversionFormat: "mp3",
    conversionMimeType: "audio/mpeg",
  },
  ape: {
    name: "ape",
    mimeType: "audio/ape",
    isNative: false,
    requiredConversion: true,
    conversionFormat: "mp3",
    conversionMimeType: "audio/mpeg",
    type: "audio",
  },
} as const;

export function getFormatInfo(format: SupportedFileFormat): FormatInfo {
  if (isAudioFileFormat(format)) {
    return SUPPORTED_AUDIO_FORMATS[format];
  } else if (isVideoFileFormat(format)) {
    return SUPPORTED_VIDEO_FORMATS[format];
  }
  return TORRENT_FORMAT.torrent;
}

export const detectMediaType = (
  file: File | string
): "video" | "audio" | "torrent" | undefined => {
  if (typeof file === "string") {
    const extension = getFileFormatFromUrl(file);
    if (!extension) return;

    if (extension === "torrent") return "torrent";

    if (Object.hasOwn(SUPPORTED_VIDEO_FORMATS, extension)) return "video";

    if (Object.hasOwn(SUPPORTED_AUDIO_FORMATS, extension)) return "audio";

    return;
  }

  return file.type.startsWith("video/") ? "video" : "audio";
};

export function isFormatSupported(
  format: string
): format is SupportedFileFormat {
  return (
    Object.hasOwn(SUPPORTED_VIDEO_FORMATS, format) ||
    Object.hasOwn(SUPPORTED_AUDIO_FORMATS, format) ||
    Object.hasOwn(TORRENT_FORMAT, format)
  );
}

export function getMimeType(format: SupportedFileFormat): string | undefined {
  if (isAudioFileFormat(format)) {
    return SUPPORTED_AUDIO_FORMATS[format].mimeType;
  }
  return SUPPORTED_VIDEO_FORMATS[format as VideoFileFormat].mimeType;
}

export function isAudioFileFormat(format: string): format is AudioFileFormat {
  return Object.hasOwn(SUPPORTED_AUDIO_FORMATS, format);
}

export function isVideoFileFormat(format: string): format is VideoFileFormat {
  return Object.hasOwn(SUPPORTED_VIDEO_FORMATS, format);
}

export const getFileName = (file: File | string): string | undefined => {
  return typeof file === "string" ? getFileNameFromUrl(file) : file.name;
};

export const getFileFormatInfo = (
  file: File | string
): FormatInfo | undefined => {
  const fileName = getFileName(file);
  if (!fileName) return;
  const format = getFileFormatFromUrl(fileName);
  if (!format || !isFormatSupported(format)) return;
  return getFormatInfo(format);
};

interface CreateMediaFileArg {
  file: string | File;
  ffmpeg: FFmpeg;
  abortSignal?: AbortSignal;
  onStatusChange: (status: FfmpegStatus) => void;
}

// New interface for streaming media file creation
interface CreateStreamingMediaFileArg {
  file: string | File;
  ffmpeg: FFmpeg;
  abortSignal?: AbortSignal;
  onStatusChange: (status: FfmpegStatus) => void;
  enableStreaming?: boolean; // Optional flag to enable streaming
}

// Extended MediaFile interface to support streaming
export interface StreamingMediaFile extends MediaFile {
  isStreaming?: boolean;
  streamingComponent?: React.ComponentType<any>;
}

export async function createMediaFile({
  file,
  ffmpeg,
  onStatusChange,
  abortSignal,
}: CreateMediaFileArg): Promise<MediaFile | undefined> {
  const fileName = getFileName(file);
  const formatInfo = getFileFormatInfo(file);
  const mediaType = detectMediaType(fileName ?? file);

  if (!fileName || !formatInfo || !mediaType) {
    notify.error("Unsupported file format");
    return;
  }
  let url;
  let outputMimeType;

  if (formatInfo.requiredConversion) {
    if (!ffmpeg.loaded) {
      onStatusChange({ status: "loading", progress: null });
      await loadFfmpeg({ ffmpeg });
    }

    outputMimeType = formatInfo.conversionMimeType;

    url = await transcodeFile({
      ffmpeg,
      file,
      outputFileFormat: formatInfo.conversionFormat,
      inputFileName: fileName,
      outputMimeType: formatInfo.conversionMimeType,
      onStatusChange,
      abortSignal,
    });
  }
  if (!outputMimeType) {
    if (isMediaSupportedByPlayer(formatInfo.mimeType)) {
      outputMimeType = formatInfo.mimeType;
    } else {
      outputMimeType = mediaType === "video" ? "video/mp4" : "audio/mpeg";
    }
  }

  url ??= typeof file === "string" ? file : URL.createObjectURL(file);

  const src =
    typeof url === "string" && mediaUrlHasSupportedFormatExtension(url)
      ? url
      : { src: url, type: outputMimeType as PlayerSupportedMimeType };

  return {
    name: fileName,
    src,
    type: mediaType,
    format: formatInfo.name,
    size: file instanceof File ? file.size : undefined,
  };
}

/**
 * Enhanced version of createMediaFile that supports streaming transcoding
 * Falls back to regular transcoding if streaming is not supported or fails
 */
export async function createMediaFileWithStreaming({
  file,
  ffmpeg,
  onStatusChange,
  abortSignal,
  enableStreaming = false,
}: CreateStreamingMediaFileArg): Promise<StreamingMediaFile | undefined> {
  const fileName = getFileName(file);
  const formatInfo = getFileFormatInfo(file);
  const mediaType = detectMediaType(fileName ?? file);

  if (!fileName || !formatInfo || !mediaType) {
    notify.error("Unsupported file format");
    return;
  }

  // Check if streaming is requested and supported
  const shouldUseStreaming =
    enableStreaming &&
    mediaType === "video" &&
    formatInfo.requiredConversion &&
    window.MediaSource &&
    typeof file !== "string"; // Only support streaming for local files for now

  if (shouldUseStreaming) {
    try {
      // Import the streaming component dynamically to avoid circular dependencies
      const { StreamingMediaPlayerContainer } = await import(
        "./components/StreamingMediaPlayerContainer"
      );

      // Return a special MediaFile that indicates streaming should be used
      const streamingMediaFile: StreamingMediaFile = {
        name: fileName,
        src: "", // Will be set by the streaming component
        type: mediaType,
        format: formatInfo.name,
        size: file instanceof File ? file.size : undefined,
        isStreaming: true,
        streamingComponent: (props: any) =>
          React.createElement(StreamingMediaPlayerContainer, {
            file,
            fileName,
            mediaType,
            ffmpeg,
            onError: async (error: Error) => {
              console.error(
                "Streaming failed, attempting fallback to regular transcoding:",
                error
              );

              // Attempt fallback to regular transcoding
              try {
                const fallbackMediaFile = await createMediaFile({
                  file,
                  ffmpeg,
                  onStatusChange,
                  abortSignal,
                });

                if (fallbackMediaFile && props.onFallback) {
                  props.onFallback(fallbackMediaFile);
                }
              } catch (fallbackError) {
                console.error(
                  "Fallback transcoding also failed:",
                  fallbackError
                );
                if (props.onFallbackFailed) {
                  props.onFallbackFailed(fallbackError);
                }
              }
            },
            ...props,
          }),
      };

      return streamingMediaFile;
    } catch (error) {
      console.warn(
        "Failed to initialize streaming, falling back to regular transcoding:",
        error
      );
      // Fall through to regular transcoding
    }
  }

  // Fall back to regular createMediaFile for non-streaming cases or if streaming fails
  return await createMediaFile({
    file,
    ffmpeg,
    onStatusChange,
    abortSignal,
  });
}

export function getFileNameWithoutExtension(fileName: string): string {
  return fileName.split(".").slice(0, -1).join(".");
}

export function isMediaSupportedByPlayer(mimeType: string) {
  const supportedMimeTypes = [
    "audio/mpeg",
    "audio/ogg",
    "audio/3gp",
    "audio/mp3",
    "audio/webm",
    "audio/flac",
    "audio/object",
    "video/mp4",
    "video/webm",
    "application/dash+xml",
    "video/ogg",
    "video/3gp",
    "video/mpeg",
    "video/avi",

    "video/object",
  ];
  return supportedMimeTypes.includes(mimeType);
}

export function getSupportedFileExtensions(): string[] {
  return [
    ...Object.keys(SUPPORTED_AUDIO_FORMATS),
    ...Object.keys(SUPPORTED_VIDEO_FORMATS),
    ...Object.keys(TORRENT_FORMAT),
  ].map((format) => `.${format}`);
}

export function getMediaFileSrcUrl(mediaFile: MediaFile): string {
  if (typeof mediaFile.src === "string") {
    return mediaFile.src;
  }
  return mediaFile.src.src;
}
