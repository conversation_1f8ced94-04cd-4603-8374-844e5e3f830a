/**
 * MediaSource API wrapper for streaming video playback
 * Handles MediaSource, SourceBuffer operations, and chunk appending
 */

export interface MediaSourceManagerOptions {
  mimeType: string;
  onSourceOpen?: () => void;
  onSourceEnded?: () => void;
  onError?: (error: Error) => void;
  onUpdateEnd?: () => void;
}

export interface ChunkInfo {
  data: Uint8Array;
  index: number;
  isLast: boolean;
}

export class MediaSourceManager {
  private mediaSource: MediaSource;
  private sourceBuffer: SourceBuffer | null = null;
  private objectUrl: string | null = null;
  private mimeType: string;
  private pendingChunks: ChunkInfo[] = [];
  private isAppending = false;
  private isEnded = false;
  
  // Event handlers
  private onSourceOpen?: () => void;
  private onSourceEnded?: () => void;
  private onError?: (error: Error) => void;
  private onUpdateEnd?: () => void;

  constructor(options: MediaSourceManagerOptions) {
    this.mimeType = options.mimeType;
    this.onSourceOpen = options.onSourceOpen;
    this.onSourceEnded = options.onSourceEnded;
    this.onError = options.onError;
    this.onUpdateEnd = options.onUpdateEnd;

    // Check MediaSource support
    if (!window.MediaSource) {
      throw new Error("MediaSource API is not supported in this browser");
    }

    // Check codec support
    if (!MediaSource.isTypeSupported(this.mimeType)) {
      throw new Error(`MIME type ${this.mimeType} is not supported`);
    }

    this.mediaSource = new MediaSource();
    this.setupEventListeners();
  }

  private setupEventListeners(): void {
    this.mediaSource.addEventListener("sourceopen", this.handleSourceOpen.bind(this));
    this.mediaSource.addEventListener("sourceended", this.handleSourceEnded.bind(this));
    this.mediaSource.addEventListener("error", this.handleError.bind(this));
  }

  private handleSourceOpen(): void {
    try {
      if (this.mediaSource.readyState !== "open") {
        return;
      }

      // Create source buffer
      this.sourceBuffer = this.mediaSource.addSourceBuffer(this.mimeType);
      
      // Set up source buffer event listeners
      this.sourceBuffer.addEventListener("updateend", this.handleUpdateEnd.bind(this));
      this.sourceBuffer.addEventListener("error", this.handleBufferError.bind(this));

      this.onSourceOpen?.();
      
      // Process any pending chunks
      this.processPendingChunks();
    } catch (error) {
      this.handleError(error as Error);
    }
  }

  private handleSourceEnded(): void {
    this.onSourceEnded?.();
  }

  private handleError(error: Error): void {
    console.error("MediaSource error:", error);
    this.onError?.(error);
  }

  private handleBufferError(event: Event): void {
    const error = new Error(`SourceBuffer error: ${event.type}`);
    this.handleError(error);
  }

  private handleUpdateEnd(): void {
    this.isAppending = false;
    this.onUpdateEnd?.();
    
    // Process next pending chunk if any
    this.processPendingChunks();
  }

  private processPendingChunks(): void {
    if (!this.sourceBuffer || this.isAppending || this.pendingChunks.length === 0) {
      return;
    }

    const chunk = this.pendingChunks.shift();
    if (chunk) {
      this.appendChunkToBuffer(chunk);
    }
  }

  private appendChunkToBuffer(chunk: ChunkInfo): void {
    if (!this.sourceBuffer || this.isAppending) {
      return;
    }

    try {
      this.isAppending = true;
      this.sourceBuffer.appendBuffer(chunk.data);
      
      // If this is the last chunk, end the stream
      if (chunk.isLast && !this.isEnded) {
        this.isEnded = true;
        // End the stream after the buffer update completes
        setTimeout(() => {
          if (this.mediaSource.readyState === "open") {
            this.mediaSource.endOfStream();
          }
        }, 100);
      }
    } catch (error) {
      this.isAppending = false;
      this.handleError(error as Error);
    }
  }

  /**
   * Get the object URL for the MediaSource
   */
  public getObjectUrl(): string {
    if (!this.objectUrl) {
      this.objectUrl = URL.createObjectURL(this.mediaSource);
    }
    return this.objectUrl;
  }

  /**
   * Append a chunk to the source buffer
   */
  public appendChunk(data: Uint8Array, index: number, isLast: boolean): void {
    const chunk: ChunkInfo = { data, index, isLast };

    if (!this.sourceBuffer || this.mediaSource.readyState !== "open") {
      // Queue the chunk for later processing
      this.pendingChunks.push(chunk);
      return;
    }

    if (this.isAppending) {
      // Queue the chunk if we're currently appending
      this.pendingChunks.push(chunk);
      return;
    }

    this.appendChunkToBuffer(chunk);
  }

  /**
   * Remove buffered data to free up memory
   */
  public removeBuffer(start: number, end: number): void {
    if (!this.sourceBuffer || this.isAppending) {
      return;
    }

    try {
      this.sourceBuffer.remove(start, end);
    } catch (error) {
      console.warn("Failed to remove buffer:", error);
    }
  }

  /**
   * Get buffered time ranges
   */
  public getBuffered(): TimeRanges | null {
    return this.sourceBuffer?.buffered || null;
  }

  /**
   * Check if the MediaSource is ready to receive data
   */
  public isReady(): boolean {
    return this.mediaSource.readyState === "open" && this.sourceBuffer !== null;
  }

  /**
   * Clean up resources
   */
  public dispose(): void {
    try {
      // Clear pending chunks
      this.pendingChunks = [];
      
      // Remove source buffer
      if (this.sourceBuffer && this.mediaSource.readyState === "open") {
        this.mediaSource.removeSourceBuffer(this.sourceBuffer);
      }
      
      // End the stream if not already ended
      if (this.mediaSource.readyState === "open") {
        this.mediaSource.endOfStream();
      }
      
      // Revoke object URL
      if (this.objectUrl) {
        URL.revokeObjectURL(this.objectUrl);
        this.objectUrl = null;
      }
      
      this.sourceBuffer = null;
    } catch (error) {
      console.warn("Error during MediaSource cleanup:", error);
    }
  }

  /**
   * Get current MediaSource state
   */
  public getState(): {
    readyState: string;
    hasSourceBuffer: boolean;
    isAppending: boolean;
    pendingChunks: number;
    isEnded: boolean;
  } {
    return {
      readyState: this.mediaSource.readyState,
      hasSourceBuffer: this.sourceBuffer !== null,
      isAppending: this.isAppending,
      pendingChunks: this.pendingChunks.length,
      isEnded: this.isEnded,
    };
  }
}

/**
 * Utility function to get supported MIME types for fragmented MP4
 */
export function getSupportedFragmentedMimeTypes(): string[] {
  const candidates = [
    'video/mp4; codecs="avc1.42E01E, mp4a.40.2"', // H.264 + AAC
    'video/mp4; codecs="avc1.42E01E"', // H.264 only
    'video/mp4; codecs="mp4a.40.2"', // AAC only
    'video/mp4', // Generic MP4
  ];

  return candidates.filter(mimeType => MediaSource.isTypeSupported(mimeType));
}

/**
 * Get the best supported MIME type for the given codec info
 */
export function getBestSupportedMimeType(hasVideo: boolean, hasAudio: boolean): string {
  const supportedTypes = getSupportedFragmentedMimeTypes();
  
  if (supportedTypes.length === 0) {
    throw new Error("No supported fragmented MP4 MIME types found");
  }

  // Prefer specific codec information if available
  if (hasVideo && hasAudio && supportedTypes.includes('video/mp4; codecs="avc1.42E01E, mp4a.40.2"')) {
    return 'video/mp4; codecs="avc1.42E01E, mp4a.40.2"';
  }
  
  if (hasVideo && supportedTypes.includes('video/mp4; codecs="avc1.42E01E"')) {
    return 'video/mp4; codecs="avc1.42E01E"';
  }

  // Fall back to generic MP4
  return supportedTypes[0];
}
