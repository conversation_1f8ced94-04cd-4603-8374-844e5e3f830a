@import "@/styles/global.css";

/* Hide scrollbar for WebKit browsers (Chrome, Safari, Edge) */
.scrollbar-hide {
  -ms-overflow-style: none; /* Internet Explorer 10+ *
  scrollbar-width: none; /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none; /* Safari and Chrome */
}

/* Beautiful thin scrollbar that matches the extension theme */
.scrollbar-thin {
  /* Firefox */
  scrollbar-width: thin;
  scrollbar-color: #4b5563 #1f2937;
}

/* WebKit browsers (Chrome, Safari, Edge) */
.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: #1f2937; /* gray-800 - matches the extension background */
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: #4b5563; /* gray-600 - subtle but visible */
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: #6b7280; /* gray-500 - lighter on hover */
}

.scrollbar-thin::-webkit-scrollbar-thumb:active {
  background: #9ca3af; /* gray-400 - even lighter when clicked */
}

/* Corner where horizontal and vertical scrollbars meet */
.scrollbar-thin::-webkit-scrollbar-corner {
  background: #1f2937;
}

.loader {
  display: flex;
  justify-content: center;
  gap: 1px;
  align-items: center;
}

.loader .dots::before {
  font-size: 30px;

  content: "...";
}

.loader .dots {
  clip-path: inset(0 3ch 0 0);
  animation: dots 1s steps(4) infinite;
}

@keyframes dots {
  0% {
    clip-path: inset(0 3ch 0 0);
  }
  100% {
    clip-path: inset(0 -1ch 0 0);
  }
}
