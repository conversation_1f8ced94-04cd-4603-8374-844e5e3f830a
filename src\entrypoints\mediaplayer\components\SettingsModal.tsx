import React, { useState, useRef, useEffect } from "react";
import { Settings, ChevronDown, Zap, Play } from "lucide-react";
import { useSettings } from "@/lib/settings";

export const SettingsDropdown = () => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const {
    enableStreaming,
    chunkDurationSeconds,
    setEnableStreaming,
    setChunkDurationSeconds,
  } = useSettings();

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Settings Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 p-2 text-gray-400 hover:text-white transition-colors rounded-lg hover:bg-slate-700"
        title="Streaming Settings"
      >
        <Settings className="w-5 h-5" />
        <ChevronDown
          className={`w-4 h-4 transition-transform ${isOpen ? "rotate-180" : ""}`}
        />
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-80 bg-slate-800 rounded-lg shadow-xl border border-slate-700 z-50">
          <div className="p-4">
            {/* Header */}
            <div className="flex items-center gap-2 mb-4 pb-2 border-b border-slate-700">
              <Zap className="w-4 h-4 text-yellow-400" />
              <h3 className="text-white font-medium">Streaming Settings</h3>
            </div>

            {/* Enable Streaming Toggle */}
            <div className="mb-4">
              <div className="flex items-center justify-between mb-2">
                <label className="text-white text-sm font-medium">
                  Enable Streaming
                </label>
                <button
                  onClick={() => setEnableStreaming(!enableStreaming)}
                  className={`relative inline-flex h-5 w-9 items-center rounded-full transition-colors ${
                    enableStreaming ? "bg-blue-600" : "bg-gray-600"
                  }`}
                >
                  <span
                    className={`inline-block h-3 w-3 transform rounded-full bg-white transition-transform ${
                      enableStreaming ? "translate-x-5" : "translate-x-1"
                    }`}
                  />
                </button>
              </div>
              <p className="text-xs text-gray-400">
                Start playback while transcoding (video only)
              </p>
            </div>

            {/* Chunk Duration Slider */}
            {enableStreaming && (
              <div className="mb-4">
                <label className="block text-white text-sm font-medium mb-2">
                  Chunk Duration: {chunkDurationSeconds}s
                </label>
                <input
                  type="range"
                  min="5"
                  max="30"
                  step="1"
                  value={chunkDurationSeconds}
                  onChange={(e) =>
                    setChunkDurationSeconds(parseInt(e.target.value))
                  }
                  className="w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer"
                />
                <div className="flex justify-between text-xs text-gray-400 mt-1">
                  <span>5s</span>
                  <span>30s</span>
                </div>
              </div>
            )}

            {/* Status Info */}
            <div className="bg-slate-700 rounded-lg p-3">
              <div className="flex items-start gap-2">
                <Play className="w-3 h-3 text-blue-400 mt-0.5 flex-shrink-0" />
                <div className="text-xs text-gray-300">
                  <p className="font-medium mb-1">Status:</p>
                  <div className="space-y-1">
                    <div className="flex justify-between">
                      <span>Streaming:</span>
                      <span
                        className={
                          enableStreaming ? "text-green-400" : "text-gray-400"
                        }
                      >
                        {enableStreaming ? "Enabled" : "Disabled"}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Browser:</span>
                      <span
                        className={
                          window.MediaSource ? "text-green-400" : "text-red-400"
                        }
                      >
                        {window.MediaSource ? "Supported" : "Not Supported"}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
