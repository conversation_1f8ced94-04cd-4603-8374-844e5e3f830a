import React, { useState, useCallback, useRef } from "react";
import { Upload, Music, Video, Magnet, FolderOpen, Play } from "lucide-react";

import { getSupportedFileExtensions } from "../helper";

interface MediaLoadingInterfaceProps {
  onFileChange: (file: File | string) => void;
  onTorrentLoad: (magnetUrl: string) => void;
}

export const MediaLoadingInterface = ({
  onFileChange,
  onTorrentLoad,
}: MediaLoadingInterfaceProps) => {
  const [isDragging, setIsDragging] = useState(false);
  const [inputValue, setInputValue] = useState("");
  const [inputError, setInputError] = useState("");
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Validation function for stream URLs
  const validateStreamUrl = (
    url: string,
  ): { isValid: boolean; error?: string } => {
    try {
      const urlObj = new URL(url);

      // Check if it's HTTP or HTTPS
      if (urlObj.protocol !== "http:" && urlObj.protocol !== "https:") {
        return { isValid: false, error: "URL must be HTTP or HTTPS" };
      }

      // Check for supported file extensions
      const supportedExtensions = getSupportedFileExtensions();
      const hasSupportedExtension = supportedExtensions.some((ext) =>
        urlObj.pathname.toLowerCase().endsWith(ext),
      );

      if (!hasSupportedExtension) {
        return {
          isValid: false,
          error:
            "URL must end with a supported media extension (.m3u8, .mp4, .mp3, etc.)",
        };
      }

      return { isValid: true };
      // eslint-disable-next-line unused-imports/no-unused-vars
    } catch (_error) {
      return { isValid: false, error: "Invalid URL format" };
    }
  };

  // Validation function for magnetic links
  const validateMagnetLink = (
    magnetUrl: string,
  ): { isValid: boolean; error?: string } => {
    if (!magnetUrl.startsWith("magnet:")) {
      return { isValid: false, error: "Magnet link must start with 'magnet:'" };
    }

    if (!magnetUrl.includes("xt=urn:btih:")) {
      return {
        isValid: false,
        error: "Invalid magnet link format - missing torrent hash",
      };
    }

    if (magnetUrl.length < 50) {
      return { isValid: false, error: "Magnet link appears to be too short" };
    }

    return { isValid: true };
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setIsDragging(true);
    } else if (e.type === "dragleave") {
      setIsDragging(false);
    }
  };

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      setIsDragging(false);

      const files = e.dataTransfer.files;
      if (files.length > 0) {
        onFileChange(files[0]);
      }
    },
    [onFileChange],
  );

  const validateInput = (value: string) => {
    const trimmedValue = value.trim();

    if (!trimmedValue) {
      return { isValid: true, error: "" }; // Don't show error for empty input
    }

    // Check if it's a magnet link
    if (trimmedValue.startsWith("magnet:")) {
      return validateMagnetLink(trimmedValue);
    }

    // Otherwise, treat as stream URL
    return validateStreamUrl(trimmedValue);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);

    // Real-time validation
    const validation = validateInput(value);
    setInputError(validation.isValid ? "" : (validation.error ?? ""));
  };

  const handleInputSubmit = () => {
    const trimmedValue = inputValue.trim();

    if (!trimmedValue) {
      setInputError("Please enter a URL or magnet link");
      return;
    }

    const validation = validateInput(trimmedValue);
    if (!validation.isValid) {
      setInputError(validation.error ?? "Invalid input");
      return;
    }

    // Check if it's a magnet link
    if (trimmedValue.startsWith("magnet:")) {
      onTorrentLoad(trimmedValue);
      setInputValue("");
      setInputError("");
      return;
    }

    // Otherwise, treat as stream URL
    onFileChange(trimmedValue);
    setInputValue("");
    setInputError("");
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleInputSubmit();
    }
  };

  const handleFileSelect = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      onFileChange(file);
    }
  };

  return (
    <div
      className="h-full flex flex-col bg-slate-950 relative overflow-hidden"
      onDragEnter={handleDrag}
      onDragLeave={handleDrag}
      onDragOver={handleDrag}
      onDrop={handleDrop}
    >
      {/* Drag Overlay */}
      {isDragging && (
        <div className="absolute inset-0 bg-blue-500/20 backdrop-blur-sm z-50 flex items-center justify-center">
          <div className="text-center">
            <Upload size={64} className="mx-auto mb-4 text-blue-400" />
            <p className="text-2xl text-blue-400 font-medium mb-2">
              Drop media files here
            </p>
            <p className="text-blue-300/80">
              Video, Audio, or Torrent files supported
            </p>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="flex-1 flex flex-col items-center justify-start px-8 py-6 max-w-4xl mx-auto w-full min-h-0">
        {/* Compact Hero Section */}
        <div className="text-center mb-6">
          <div className="flex items-center justify-center gap-3 mb-3">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
              <Play size={24} className="text-white" />
            </div>
            <h1 className="text-3xl font-bold text-white">Load Your Media</h1>
          </div>
          <p className="text-lg text-slate-400">
            Stream from URLs, load magnet links, or upload local files
          </p>
        </div>

        {/* Main Input Section */}
        <div className="w-full max-w-2xl mb-6">
          <div className="flex gap-3">
            <input
              type="text"
              value={inputValue}
              onChange={handleInputChange}
              onKeyDown={handleKeyPress}
              placeholder="Enter stream URL (https://...) or magnet link (magnet:...)"
              className={`flex-1 px-6 py-4 text-lg bg-slate-800 border-2 rounded-xl text-white placeholder-slate-400 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                inputError
                  ? "border-red-500 focus:border-red-500 focus:ring-red-500"
                  : "border-slate-600 hover:border-slate-500 focus:border-blue-500"
              }`}
            />
            <button
              onClick={handleInputSubmit}
              disabled={!inputValue.trim()}
              className="px-8 py-4 bg-blue-600 hover:bg-blue-700 disabled:bg-slate-600 disabled:cursor-not-allowed text-white rounded-xl transition-colors duration-200 font-medium text-lg"
            >
              Load
            </button>
          </div>
          {inputError && (
            <p className="text-red-400 text-sm mt-2 px-2">{inputError}</p>
          )}
        </div>

        {/* Alternative Options */}
        <div className="w-full max-w-2xl">
          {/* OR Separator */}
          <div className="flex items-center justify-center mb-4">
            <div className="flex items-center gap-4">
              <div className="h-px bg-slate-600 w-16"></div>
              <span className="text-slate-400 text-sm font-medium">OR</span>
              <div className="h-px bg-slate-600 w-16"></div>
            </div>
          </div>

          {/* File Upload Options */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            {/* Browse Files */}
            <button
              onClick={handleFileSelect}
              className="p-6 bg-slate-800 hover:bg-slate-700 border-2 border-slate-600 hover:border-slate-500 rounded-xl transition-all duration-200 text-center group"
            >
              <FolderOpen
                size={32}
                className="mx-auto mb-3 text-slate-400 group-hover:text-slate-300"
              />
              <p className="text-white font-medium mb-1">Browse Files</p>
              <p className="text-slate-400 text-sm">Select from your device</p>
            </button>

            {/* Drag and Drop */}
            <div className="p-6 bg-slate-800 border-2 border-dashed border-slate-600 rounded-xl text-center">
              <Upload size={32} className="mx-auto mb-3 text-slate-400" />
              <p className="text-white font-medium mb-1">Drag & Drop</p>
              <p className="text-slate-400 text-sm">Drop files anywhere</p>
            </div>
          </div>
        </div>

        {/* Supported Formats */}
        <div className="w-full max-w-4xl">
          <h3 className="text-lg font-semibold text-white text-center mb-4">
            Supported Formats
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Video Formats */}
            <div className="bg-slate-800/50 rounded-lg p-4 text-center">
              <Video size={24} className="mx-auto mb-2 text-blue-400" />
              <h4 className="text-white font-medium mb-2 text-sm">Video</h4>
              <div className="text-xs text-slate-300">
                <p>MP4, WebM, MKV, AVI, MOV, FLV, WMV, TS, M3U8, MPD</p>
              </div>
            </div>

            {/* Audio Formats */}
            <div className="bg-slate-800/50 rounded-lg p-4 text-center">
              <Music size={24} className="mx-auto mb-2 text-green-400" />
              <h4 className="text-white font-medium mb-2 text-sm">Audio</h4>
              <div className="text-xs text-slate-300">
                <p>MP3, WAV, FLAC, AAC, OGG, M4A, OPUS, ALAC, APE</p>
              </div>
            </div>

            {/* Streaming */}
            <div className="bg-slate-800/50 rounded-lg p-4 text-center">
              <Magnet size={24} className="mx-auto mb-2 text-purple-400" />
              <h4 className="text-white font-medium mb-2 text-sm">Streaming</h4>
              <div className="text-xs text-slate-300">
                <p>HTTP/HTTPS URLs, Magnet Links, Torrent Files</p>
              </div>
            </div>
          </div>

          <p className="text-center text-slate-500 text-xs mt-4">
            Files are auto-converted if needed for optimal playback
          </p>
        </div>
      </div>

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        className="hidden"
        accept={`video/*,audio/*,${getSupportedFileExtensions().join(",")}`}
        onChange={handleFileChange}
      />
    </div>
  );
};
