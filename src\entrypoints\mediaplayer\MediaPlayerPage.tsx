import React, { useState, useRef, useEffect, useCallback } from "react";
import { PlayerTab } from "./tabs/PlayerTab";
import {
  createMediaFile,
  createMediaFileWithStreaming,
  getFileFormatInfo,
  getMediaFileSrcUrl,
  PlayerSupportedMimeType,
  StreamingMediaFile,
} from "./helper";
import { isSupportedMediaUrl, removeMediaSrcFromUrl } from "@/utils/url.utils";
import { notify } from "@/lib/notify";
import { FFmpeg } from "@ffmpeg/ffmpeg";
import { FfmpegStatus, loadFfmpeg } from "@/lib/ffmpeg";
import { logger } from "@/lib/logger";
import { Loading } from "./components/LoadingMediaProgress";
import { Header } from "./components/Header";
import { useTorrentPlayer } from "./hooks/useTorrent";
import { MediaLoadingInterface } from "./components/DefaultMediaUI";
import { MediaActionSelector } from "./components/MediaActionSelector";
import { upperCaseFirstLetter } from "@/utils/utils";
import { useSettings } from "@/lib/settings";

export interface MediaFile {
  name: string;
  src:
    | {
        src: string;
        type: PlayerSupportedMimeType;
      }
    | string;
  type: "video" | "audio" | "torrent";
  format: string;
  size?: number;
}

export const MediaPlayerPage = () => {
  const [currentMedia, setCurrentMedia] = useState<
    MediaFile | StreamingMediaFile | null
  >(null);

  const ffmpegRef = useRef<FFmpeg>(new FFmpeg());

  const [fileProcessingState, setFileProcessingState] = useState<FfmpegStatus>({
    progress: 0,
    status: "idle",
  });

  const [isStreamingActive, setIsStreamingActive] = useState(false);
  const { status, loadTorrent, cancelTorrent, progress } = useTorrentPlayer(
    (file) => {
      setCurrentMedia(file);
    }
  );
  const ffmpegLoadingAbortControllerRef = useRef<AbortController>(
    new AbortController()
  );

  const mediaSourceUrl = new URLSearchParams(window.location.search).get("src");

  const isMediaSourceUrlValid =
    mediaSourceUrl && isSupportedMediaUrl(mediaSourceUrl) && !currentMedia;

  const cleanup = useCallback(() => {
    ffmpegLoadingAbortControllerRef.current = new AbortController();

    if (currentMedia) {
      URL.revokeObjectURL(getMediaFileSrcUrl(currentMedia));
    }
    setFileProcessingState({
      progress: null,
      status: "idle",
    });
    setCurrentMedia(null);
    setIsStreamingActive(false);
  }, [currentMedia]);

  const handleCancelFileProcessing = useCallback(() => {
    ffmpegRef.current.terminate();
    ffmpegLoadingAbortControllerRef.current.abort();
    notify.info("Transcoding cancelled");
    cleanup();
  }, [cleanup]);

  const handleLoadMediaClick = useCallback(() => {
    // Cancel any ongoing torrent loading
    if (status === "loading" || status === "streaming") {
      cancelTorrent();
    }
    cleanup();

    notify.info("Ready to load new media");
  }, [status, cancelTorrent, cleanup]);

  const handleCancelTorrent = useCallback(() => {
    cancelTorrent();
    notify.info("Torrent loading cancelled");
    setCurrentMedia(null);
  }, [cancelTorrent]);

  const handleFFmpegProgress = useCallback((progress: number) => {
    setFileProcessingState((prev) => {
      const correctProgress =
        progress >= 100 && prev.progress === null ? 0 : progress;
      return {
        ...prev,
        progress: correctProgress,
      };
    });
  }, []);

  const handleProcessingStatusChange = (status: FfmpegStatus) => {
    setFileProcessingState(status);
  };

  const handleFileChange = async (file: File | string) => {
    cleanup();

    try {
      if (typeof file === "string") {
        const isValid = isSupportedMediaUrl(file);
        if (!isValid) {
          notify.error("Invalid URL");
          return;
        }
      }

      const fileFormatInfo = getFileFormatInfo(file);

      if (fileFormatInfo?.name === "torrent") {
        loadTorrent(file);
        return;
      }

      // Determine media type for streaming check
      const fileName = typeof file === "string" ? file : file.name;
      const mediaType =
        fileName?.includes("video") || fileFormatInfo?.type === "video"
          ? "video"
          : "audio";

      // Check if streaming should be enabled (we'll use settings directly here)
      const { enableStreaming } = useSettings.getState();
      const shouldUseStreaming =
        enableStreaming &&
        mediaType === "video" &&
        typeof file !== "string" &&
        !!window.MediaSource;

      console.log("🎬 Media Processing Debug:", {
        fileName,
        mediaType,
        enableStreaming,
        isLocalFile: typeof file !== "string",
        hasMediaSource: !!window.MediaSource,
        shouldUseStreaming,
        requiresConversion: fileFormatInfo?.requiredConversion,
      });

      // Set streaming state
      setIsStreamingActive(shouldUseStreaming);

      // Use streaming-enabled function if streaming is requested
      const mediaFile = await createMediaFileWithStreaming({
        ffmpeg: ffmpegRef.current,
        file,
        onStatusChange: shouldUseStreaming
          ? (_status: FfmpegStatus) => {} // Don't update processing state for streaming
          : handleProcessingStatusChange,
        abortSignal: ffmpegLoadingAbortControllerRef.current.signal,
        enableStreaming: shouldUseStreaming,
      });

      if (!mediaFile) {
        setIsStreamingActive(false);
        return;
      }

      setCurrentMedia(mediaFile);

      // For streaming, don't show loading state
      if (!shouldUseStreaming) {
        setTimeout(() => {
          setFileProcessingState({
            progress: null,
            status: "idle",
          });
        }, 1000);
      } else {
        // For streaming, immediately set to idle
        setFileProcessingState({
          progress: null,
          status: "idle",
        });
      }
    } catch (err) {
      logger.error(err);
      setIsStreamingActive(false);
      cleanup();
    }
  };

  useEffect(() => {
    const ffmpeg = ffmpegRef.current;
    ffmpeg.on("log", ({ message }) => {
      // console.log("FFmpeg log", message);
    });
    ffmpeg.on("progress", ({ progress }) => {
      const correctProgress = progress > 1 ? 1 : progress;
      handleFFmpegProgress(Math.round(correctProgress * 100));
    });
    loadFfmpeg({
      ffmpeg,
    })
      .then(() => {
        console.log("FFmpeg loaded");
      })
      .catch((err: unknown) => {
        logger.error(err);
        cleanup();
      });

    if (isMediaSourceUrlValid) {
      removeMediaSrcFromUrl();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className="w-full  h-screen scrollbar-thin bg-slate-950 text-white flex flex-col relative overflow-hidden">
      {/* Transcoding Overlay - Don't show when streaming is active */}
      {fileProcessingState.status !== "idle" && !isStreamingActive && (
        <Loading
          progress={fileProcessingState.progress}
          message={upperCaseFirstLetter(fileProcessingState.status)}
          description={
            fileProcessingState.status === "transcoding"
              ? "Converting file format"
              : "Downloading file"
          }
          onCancel={handleCancelFileProcessing}
        />
      )}

      {/* Torrent Overlay */}
      {status === "loading" && (
        <Loading
          progress={progress}
          message="Loading torrent"
          description="Downloading and preparing media"
          onCancel={handleCancelTorrent}
        />
      )}

      {/* Header */}
      <Header onLoadMediaClick={handleLoadMediaClick} />

      {/* Content Area */}
      {isMediaSourceUrlValid ? (
        <div className="flex-1 overflow-hidden">
          <MediaActionSelector
            mediaUrl={mediaSourceUrl}
            onPlay={handleFileChange}
          />
        </div>
      ) : (
        <div className="flex-1 overflow-hidden">
          {currentMedia ? (
            <PlayerTab currentMedia={currentMedia} />
          ) : (
            <MediaLoadingInterface
              onFileChange={handleFileChange}
              onTorrentLoad={loadTorrent}
            />
          )}
        </div>
      )}
    </div>
  );
};
