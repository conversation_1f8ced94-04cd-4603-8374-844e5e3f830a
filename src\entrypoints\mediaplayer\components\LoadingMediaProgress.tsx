import React from "react";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";

type Prop = {
  progress: number | null;
  message: string;
  description?: string;
  onCancel?: () => void;
};

export const Loading = ({ progress, message, description, onCancel }: Prop) => {
  return (
    <div className="absolute inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center">
      <div className="bg-slate-900 border border-slate-700 rounded-xl p-8 max-w-sm w-full mx-4 relative">
        <div className="text-center">
          {/* Spinning Loader with Progress Ring */}
          {progress !== null ? (
            <LoaderWithProgressRing progress={progress} />
          ) : (
            <div className="w-16 h-16 mx-auto mb-6">
              <Loader2 className="w-16 h-16 text-blue-500 animate-spin" />
            </div>
          )}
          {/* Status Text */}
          <div className="loader mb-2">
            <span className="text-lg font-semibold text-white ">{message}</span>
            <span className="dots"></span>
          </div>
          {description && (
            <p className="text-sm text-slate-400 mb-4">{description}</p>
          )}
        </div>
        {onCancel && (
          <Button
            onClick={onCancel}
            className="w-full mt-6"
            aria-label="Cancel"
            variant="secondary"
          >
            Cancel
          </Button>
        )}
      </div>
    </div>
  );
};

function LoaderWithProgressRing({ progress }: { progress: number }) {
  return (
    <div className="relative w-20 h-20 mx-auto mb-6">
      {/* <Loader2 className="w-20 h-20 text-blue-500 animate-spin" /> */}

      {/* Progress Ring */}
      <svg
        className="absolute inset-0 w-20 h-20 transform -rotate-90"
        viewBox="0 0 80 80"
      >
        {/* Background circle */}
        <circle
          cx="40"
          cy="40"
          r="36"
          stroke="rgba(148, 163, 184, 0.2)"
          strokeWidth="4"
          fill="none"
        />
        {/* Progress circle */}
        <circle
          cx="40"
          cy="40"
          r="36"
          stroke="url(#progressGradient)"
          strokeWidth="4"
          fill="none"
          strokeLinecap="round"
          strokeDasharray={`${2 * Math.PI * 36}`}
          strokeDashoffset={`${2 * Math.PI * 36 * (1 - progress / 100)}`}
          className="transition-all duration-300 ease-out"
        />
        {/* Gradient definition */}
        <defs>
          <linearGradient
            id="progressGradient"
            x1="0%"
            y1="0%"
            x2="100%"
            y2="0%"
          >
            <stop offset="0%" stopColor="#3b82f6" />
            <stop offset="50%" stopColor="#8b5cf6" />
            <stop offset="100%" stopColor="#06b6d4" />
          </linearGradient>
        </defs>
      </svg>

      {/* Progress Text */}
      <div className="absolute inset-0 flex flex-col items-center justify-center">
        <span className="text-lg font-bold text-white">
          {Math.round(progress)}%
        </span>
      </div>
    </div>
  );
}
