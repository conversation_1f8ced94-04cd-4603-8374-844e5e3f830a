import React, { useState } from "react";
import { Settings, X, Play, Zap } from "lucide-react";
import { useSettings } from "@/lib/settings";

interface SettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const SettingsModal = ({ isOpen, onClose }: SettingsModalProps) => {
  const {
    enableStreaming,
    chunkDurationSeconds,
    setEnableStreaming,
    setChunkDurationSeconds,
    resetToDefaults,
  } = useSettings();

  const [tempChunkDuration, setTempChunkDuration] = useState(chunkDurationSeconds);

  const handleSave = () => {
    setChunkDurationSeconds(tempChunkDuration);
    onClose();
  };

  const handleReset = () => {
    resetToDefaults();
    setTempChunkDuration(10);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-slate-800 rounded-lg p-6 w-full max-w-md mx-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-2">
            <Settings className="w-5 h-5 text-blue-400" />
            <h2 className="text-xl font-semibold text-white">Settings</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Streaming Settings */}
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium text-white mb-4 flex items-center gap-2">
              <Zap className="w-4 h-4 text-yellow-400" />
              Streaming Playback
            </h3>
            
            {/* Enable Streaming Toggle */}
            <div className="flex items-center justify-between mb-4">
              <div>
                <label className="text-white font-medium">Enable Streaming</label>
                <p className="text-sm text-gray-400 mt-1">
                  Start playback while transcoding (experimental)
                </p>
              </div>
              <button
                onClick={() => setEnableStreaming(!enableStreaming)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  enableStreaming ? "bg-blue-600" : "bg-gray-600"
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    enableStreaming ? "translate-x-6" : "translate-x-1"
                  }`}
                />
              </button>
            </div>

            {/* Chunk Duration */}
            {enableStreaming && (
              <div className="mb-4">
                <label className="block text-white font-medium mb-2">
                  Chunk Duration: {tempChunkDuration} seconds
                </label>
                <input
                  type="range"
                  min="5"
                  max="30"
                  step="1"
                  value={tempChunkDuration}
                  onChange={(e) => setTempChunkDuration(parseInt(e.target.value))}
                  className="w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer slider"
                />
                <div className="flex justify-between text-xs text-gray-400 mt-1">
                  <span>5s (faster start)</span>
                  <span>30s (better quality)</span>
                </div>
              </div>
            )}

            {/* Info Box */}
            <div className="bg-slate-700 rounded-lg p-4 mb-4">
              <div className="flex items-start gap-2">
                <Play className="w-4 h-4 text-blue-400 mt-0.5 flex-shrink-0" />
                <div className="text-sm text-gray-300">
                  <p className="font-medium mb-1">How Streaming Works:</p>
                  <ul className="space-y-1 text-xs">
                    <li>• Video starts playing while still transcoding</li>
                    <li>• Currently supports video files only</li>
                    <li>• Falls back to regular mode if streaming fails</li>
                    <li>• Requires modern browser with MediaSource API</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Browser Support Check */}
            <div className="text-sm">
              <span className="text-gray-400">Browser Support: </span>
              <span className={window.MediaSource ? "text-green-400" : "text-red-400"}>
                {window.MediaSource ? "✓ Supported" : "✗ Not Supported"}
              </span>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex gap-3 mt-6">
          <button
            onClick={handleReset}
            className="flex-1 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            Reset to Defaults
          </button>
          <button
            onClick={handleSave}
            className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Save Settings
          </button>
        </div>
      </div>
    </div>
  );
};

export const SettingsButton = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <>
      <button
        onClick={() => setIsModalOpen(true)}
        className="p-2 text-gray-400 hover:text-white transition-colors rounded-lg hover:bg-slate-700"
        title="Settings"
      >
        <Settings className="w-5 h-5" />
      </button>
      
      <SettingsModal 
        isOpen={isModalOpen} 
        onClose={() => setIsModalOpen(false)} 
      />
    </>
  );
};
