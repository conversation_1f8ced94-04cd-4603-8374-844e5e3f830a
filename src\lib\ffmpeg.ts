/* eslint-disable @typescript-eslint/no-unnecessary-condition */
import { fetchFile, toBlobURL } from "@ffmpeg/util";
import { FFmpeg } from "@ffmpeg/ffmpeg";
import { logger } from "./logger";

// ------------------------ Interfaces & Types ------------------------

export interface CodecInfo {
  hasVideo: boolean;
  hasAudio: boolean;
  videoCodec?: string;
  audioCodec?: string;
}

interface TranscodeStrategyParams {
  codecInfo: CodecInfo;
  inputFileName: string;
  outputFileName: string;
  outputFormat: OutputFileFormat;
}

export type OutputFileFormat = "mp4" | "mp3";

interface TranscodeArgs {
  ffmpeg: FFmpeg;
  file: string | File;
  inputFileName: string;
  outputFileFormat: OutputFileFormat;
  outputMimeType: string;
  onStatusChange: (status: FfmpegStatus) => void;
  abortSignal?: AbortSignal;
}

// New interfaces for chunked transcoding
export interface ChunkedTranscodeArgs {
  ffmpeg: FFmpeg;
  file: string | File;
  inputFileName: string;
  outputMimeType: string;
  onStatusChange: (status: FfmpegStatus) => void;
  onChunkReady: (
    chunk: Uint8Array,
    chunkIndex: number,
    isLast: boolean
  ) => void;
  chunkDurationSeconds?: number;
  abortSignal?: AbortSignal;
}

export interface FfmpegStatus {
  progress: number | null;
  status: "loading" | "transcoding" | "idle";
}

interface LoadFfmpegArgs {
  ffmpeg: FFmpeg;
}

export async function loadFfmpeg({ ffmpeg }: LoadFfmpegArgs) {
  if (ffmpeg.loaded) {
    console.log("Already ffmpeg loaded");
    return;
  }
  console.log("Loading ffmpeg.....");

  try {
    await ffmpeg.load({
      coreURL: browser.runtime.getURL("/lib/ffmpeg-core.js"),
      wasmURL: browser.runtime.getURL("/lib/ffmpeg-core.wasm"),
      workerURL: browser.runtime.getURL("/lib/ffmpeg-core.worker.js"),
    });
    console.log("FFmpeg loaded");
  } catch (err) {
    console.error("Failed to load FFmpeg:", err);
    throw err;
  }
}

export async function transcodeFile({
  ffmpeg,
  file,
  inputFileName,
  outputFileFormat,
  onStatusChange,
  outputMimeType,
  abortSignal,
}: TranscodeArgs): Promise<string> {
  if (!ffmpeg.loaded) {
    throw new Error("FFmpeg is not loaded.");
  }

  try {
    const outputFileName = `output.${outputFileFormat}`;

    await loadInputFile({
      ffmpeg,
      file,
      inputFileName,
      onStatusChange,
      abortSignal,
    });

    const codecInfo = await extractCodecInfo(ffmpeg, inputFileName);

    const ffmpegArgs = determineTranscodeStrategy({
      codecInfo,
      inputFileName,
      outputFileName,
      outputFormat: outputFileFormat,
    });

    onStatusChange({ progress: 0, status: "transcoding" });
    updateStatusOnTranscodingProgress(ffmpeg, onStatusChange);
    await ffmpeg.exec(ffmpegArgs);
    return await getOutputURL(ffmpeg, outputFileName, outputMimeType);
  } catch (error) {
    console.error("Failed to transcode file: ", error);
    throw error;
  } finally {
    onStatusChange({ progress: 0, status: "idle" });
  }
}

// ------------------------ Helper Functions ------------------------
function updateStatusOnTranscodingProgress(
  ffmpeg: FFmpeg,
  onStatusChange: (status: FfmpegStatus) => void
) {
  ffmpeg.on("progress", ({ progress }) => {
    const correctProgress = (progress > 1 ? 1 : progress) * 100;
    onStatusChange({ progress: correctProgress, status: "transcoding" });
  });
}

type LoadInputFileArgs = {
  ffmpeg: FFmpeg;
  file: string | File;
  inputFileName: string;
  onStatusChange: (status: FfmpegStatus) => void;
  abortSignal?: AbortSignal;
};
async function loadInputFile({
  ffmpeg,
  file,
  inputFileName,
  onStatusChange,
  abortSignal,
}: LoadInputFileArgs): Promise<void> {
  onStatusChange({ progress: 0, status: "loading" });
  if (typeof file === "string") {
    await loadFileWithProgress({
      url: file,
      ffmpeg,
      inputFileName,
      abortSignal,
      onProgress: (progress) => {
        console.log("Loading progress", progress);
        onStatusChange({ progress, status: "loading" });
      },
    });
    onStatusChange({ progress: 100, status: "loading" });
    return;
  }
  const data = await fetchFile(file);
  await ffmpeg.writeFile(inputFileName, data);
  onStatusChange({ progress: 100, status: "loading" });
}

async function extractCodecInfo(
  ffmpeg: FFmpeg,
  inputFileName: string
): Promise<CodecInfo> {
  const stderrLines: string[] = [];

  ffmpeg.on("log", ({ message }) => {
    stderrLines.push(message);
  });

  try {
    await ffmpeg.exec(["-i", inputFileName]);
  } catch {
    // We expect ffmpeg to error when probing, ignore
  }

  console.log("stderrLines", stderrLines);

  const output = stderrLines.join("\n");
  const hasVideo = /Stream #\d+:\d+.*Video:/i.test(output);
  const hasAudio = /Stream #\d+:\d+.*Audio:/i.test(output);

  const videoCodec = /Video:\s?([a-zA-Z0-9]+)/.exec(output)?.[1];
  const audioCodec = /Audio:\s?([a-zA-Z0-9]+)/.exec(output)?.[1];

  return { hasVideo, hasAudio, videoCodec, audioCodec };
}

// Add this simple function to your existing code
function canRemuxToMP4(videoCodec?: string, audioCodec?: string) {
  const mp4VideoCodecs = ["h264", "avc1", "h265", "hevc", "av01", "mp4v"];
  const mp4AudioCodecs = ["aac", "mp3", "ac3", "mp4a"];

  const videoOK = videoCodec
    ? mp4VideoCodecs.includes(videoCodec.toLowerCase())
    : true;
  const audioOK = audioCodec
    ? mp4AudioCodecs.includes(audioCodec.toLowerCase())
    : true;

  return videoOK && audioOK;
}

// Replace your existing determineTranscodeStrategy function with this improved version
function determineTranscodeStrategy({
  codecInfo,
  inputFileName,
  outputFileName,
  outputFormat,
}: TranscodeStrategyParams): string[] {
  const { hasVideo, hasAudio, videoCodec, audioCodec } = codecInfo;

  if (hasVideo && outputFormat === "mp4") {
    // Use the simple compatibility check
    if (canRemuxToMP4(videoCodec, audioCodec)) {
      console.log(`Can remux: ${videoCodec}/${audioCodec} -> MP4`);
      return [
        "-i",
        inputFileName,
        "-c",
        "copy",
        "-movflags",
        "+faststart",
        outputFileName,
      ];
    }

    console.log(`Need re-encode: ${videoCodec}/${audioCodec} -> MP4`);
    return [
      "-i",
      inputFileName,
      "-c:v",
      "libx264",
      "-c:a",
      "aac",
      "-movflags",
      "+faststart",
      outputFileName,
    ];
  }

  if (!hasVideo && hasAudio && outputFormat === "mp3") {
    if (audioCodec === "mp3") {
      console.log("Can copy MP3 audio");
      return ["-i", inputFileName, "-c", "copy", outputFileName];
    }

    console.log(`Need re-encode: ${audioCodec} -> MP3`);
    return ["-i", inputFileName, "-c:a", "libmp3lame", outputFileName];
  }

  throw new Error("Unsupported codec or format combination.");
}
async function getOutputURL(
  ffmpeg: FFmpeg,
  outputFileName: string,
  outputMimeType: string
): Promise<string> {
  const data = (await ffmpeg.readFile(outputFileName)) as {
    buffer: ArrayBuffer;
  };
  return URL.createObjectURL(new Blob([data.buffer], { type: outputMimeType }));
}

export function convertToBlobUrl(
  url: string,
  mimeType: string
): Promise<string> {
  try {
    return toBlobURL(url, mimeType);
  } catch (error: unknown) {
    logger.error("Failed to convert to blob url: ", error);
    throw error;
  }
}

type LoadFileWithProgressArgs = {
  url: string;
  ffmpeg: FFmpeg;
  inputFileName: string;
  onProgress: (progress: number) => void;
  abortSignal?: AbortSignal;
};
async function loadFileWithProgress({
  url,
  ffmpeg,
  inputFileName,
  onProgress,
  abortSignal,
}: LoadFileWithProgressArgs) {
  const res = await fetch(url, { signal: abortSignal });
  const contentLength = parseInt(res.headers.get("Content-Length") ?? "0", 10);

  let received = 0;
  const chunks: Uint8Array[] = [];

  const resBody = res.body;
  if (!resBody) {
    throw new Error("No response body");
  }
  const reader = resBody.getReader();

  while (true) {
    const { done, value } = await reader.read();
    if (done || abortSignal?.aborted) break;
    if (value) {
      chunks.push(value);
      received += value.length;
      const progress = (received / contentLength) * 100;
      onProgress(progress);
    }
  }

  const fileData = new Uint8Array(received);
  let offset = 0;
  for (const chunk of chunks) {
    fileData.set(chunk, offset);
    offset += chunk.length;
  }

  await ffmpeg.writeFile(inputFileName, fileData);
}

// ------------------------ Chunked Transcoding Functions ------------------------

/**
 * Transcodes a file in chunks for streaming playback using Media Source API
 * This function processes the file in segments and calls onChunkReady for each completed chunk
 */
export async function transcodeFileInChunks({
  ffmpeg,
  file,
  inputFileName,
  outputMimeType: _outputMimeType,
  onStatusChange,
  onChunkReady,
  chunkDurationSeconds = 10,
  abortSignal,
}: ChunkedTranscodeArgs): Promise<void> {
  if (!ffmpeg.loaded) {
    throw new Error("FFmpeg is not loaded.");
  }

  try {
    // Load the input file
    await loadInputFile({
      ffmpeg,
      file,
      inputFileName,
      onStatusChange,
      abortSignal,
    });

    // Get codec info to determine transcoding strategy
    const codecInfo = await extractCodecInfo(ffmpeg, inputFileName);

    // Get file duration for chunk calculation
    const duration = await getFileDuration(ffmpeg, inputFileName);
    if (!duration) {
      throw new Error("Could not determine file duration");
    }

    const totalChunks = Math.ceil(duration / chunkDurationSeconds);

    onStatusChange({ progress: 0, status: "transcoding" });

    // Process each chunk
    for (let chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++) {
      if (abortSignal?.aborted) {
        throw new Error("Transcoding aborted");
      }

      const startTime = chunkIndex * chunkDurationSeconds;
      const chunkDuration = Math.min(
        chunkDurationSeconds,
        duration - startTime
      );
      const isLastChunk = chunkIndex === totalChunks - 1;

      const chunkFileName = `chunk_${chunkIndex}.mp4`;

      // Generate FFmpeg args for this chunk
      const ffmpegArgs = determineChunkedTranscodeStrategy({
        codecInfo,
        inputFileName,
        outputFileName: chunkFileName,
        startTime,
        duration: chunkDuration,
        isFirstChunk: chunkIndex === 0,
      });

      // Transcode this chunk
      await ffmpeg.exec(ffmpegArgs);

      // Read the chunk data
      const chunkData = (await ffmpeg.readFile(chunkFileName)) as {
        buffer: ArrayBuffer;
      };

      // Clean up the chunk file
      await ffmpeg.deleteFile(chunkFileName);

      // Notify that chunk is ready
      onChunkReady(new Uint8Array(chunkData.buffer), chunkIndex, isLastChunk);

      // Update progress
      const progress = ((chunkIndex + 1) / totalChunks) * 100;
      onStatusChange({ progress, status: "transcoding" });
    }
  } catch (error) {
    console.error("Failed to transcode file in chunks: ", error);
    throw error;
  } finally {
    onStatusChange({ progress: 0, status: "idle" });
  }
}

/**
 * Gets the duration of a media file in seconds
 */
async function getFileDuration(
  ffmpeg: FFmpeg,
  inputFileName: string
): Promise<number | null> {
  const stderrLines: string[] = [];

  ffmpeg.on("log", ({ message }) => {
    stderrLines.push(message);
  });

  try {
    await ffmpeg.exec(["-i", inputFileName]);
  } catch {
    // We expect ffmpeg to error when probing, ignore
  }

  const output = stderrLines.join("\n");
  const durationMatch = /Duration: (\d{2}):(\d{2}):(\d{2})\.(\d{2})/.exec(
    output
  );

  if (durationMatch) {
    const hours = parseInt(durationMatch[1], 10);
    const minutes = parseInt(durationMatch[2], 10);
    const seconds = parseInt(durationMatch[3], 10);
    const centiseconds = parseInt(durationMatch[4], 10);

    return hours * 3600 + minutes * 60 + seconds + centiseconds / 100;
  }

  return null;
}

interface ChunkedTranscodeStrategyParams {
  codecInfo: CodecInfo;
  inputFileName: string;
  outputFileName: string;
  startTime: number;
  duration: number;
  isFirstChunk: boolean;
}

/**
 * Determines FFmpeg arguments for chunked transcoding with fragmented MP4 output
 */
function determineChunkedTranscodeStrategy({
  codecInfo,
  inputFileName,
  outputFileName,
  startTime,
  duration,
  isFirstChunk: _isFirstChunk,
}: ChunkedTranscodeStrategyParams): string[] {
  const { hasVideo, hasAudio: _hasAudio, videoCodec, audioCodec } = codecInfo;

  if (!hasVideo) {
    throw new Error("Chunked transcoding currently only supports video files");
  }

  const baseArgs = [
    "-i",
    inputFileName,
    "-ss",
    startTime.toString(),
    "-t",
    duration.toString(),
  ];

  // Use fragmented MP4 for streaming
  const fragmentArgs = [
    "-movflags",
    "frag_keyframe+empty_moov+default_base_moof",
    "-f",
    "mp4",
  ];

  // Determine codec strategy
  if (canRemuxToMP4(videoCodec, audioCodec)) {
    // Can copy streams without re-encoding
    return [...baseArgs, "-c", "copy", ...fragmentArgs, outputFileName];
  } else {
    // Need to re-encode
    return [
      ...baseArgs,
      "-c:v",
      "libx264",
      "-c:a",
      "aac",
      "-preset",
      "ultrafast", // Fast encoding for streaming
      "-crf",
      "23", // Good quality/size balance
      ...fragmentArgs,
      outputFileName,
    ];
  }
}
