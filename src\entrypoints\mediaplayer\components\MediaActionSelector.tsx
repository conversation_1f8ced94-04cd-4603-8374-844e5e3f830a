import React from "react";
import { Play, Download, FileVideo, FileAudio } from "lucide-react";
import { getFileNameFromUrl } from "@/utils/url.utils";
import { getFileFormatFromUrl } from "@/utils/url.utils";
import { isAudioFileFormat } from "../helper";

interface MediaActionSelectorProps {
  mediaUrl: string;
  onPlay: (url: string) => void;
}

export const MediaActionSelector = ({
  mediaUrl,
  onPlay,
}: MediaActionSelectorProps) => {
  const fileName = getFileNameFromUrl(mediaUrl) ?? "Unknown Media";
  const fileFormat = getFileFormatFromUrl(mediaUrl);
  const isAudio = fileFormat ? isAudioFileFormat(fileFormat) : false;

  const handleDownload = async () => {
    try {
      // Use browser downloads API if available (extension context)
      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
      if (typeof browser !== "undefined" && browser.downloads) {
        await browser.downloads.download({
          url: mediaUrl,
          filename: fileName,
        });
      } else {
        // Fallback to anchor element method
        const link = document.createElement("a");
        link.href = mediaUrl;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    } catch (error) {
      console.error("Download failed:", error);
      // Fallback to anchor element method
      const link = document.createElement("a");
      link.href = mediaUrl;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  return (
    <div className="h-full bg-gradient-to-br from-slate-950 via-slate-900 to-slate-950 flex items-center justify-center p-8">
      <div className="max-w-2xl w-full">
        {/* Media Info Card */}
        <div className="bg-slate-800/50 backdrop-blur-sm rounded-2xl border border-slate-700/50 p-8 shadow-2xl">
          {/* Media Icon and Title */}
          <div className="text-center mb-6">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-slate-700/50 rounded-full mb-4">
              {isAudio ? (
                <FileAudio className="w-8 h-8 text-blue-400" />
              ) : (
                <FileVideo className="w-8 h-8 text-purple-400" />
              )}
            </div>
            <h1 className="text-2xl font-bold text-white mb-2">{fileName}</h1>
            <div className="flex items-center justify-center gap-2 text-slate-400">
              <span className="capitalize">
                {isAudio ? "Audio" : "Video"} File
              </span>
              {fileFormat && (
                <>
                  <span>•</span>
                  <span className="uppercase">{fileFormat}</span>
                </>
              )}
            </div>
          </div>

          {/* Compact Action Buttons */}
          <div className="flex gap-3 mb-6">
            <button
              onClick={() => {
                onPlay(mediaUrl);
              }}
              className="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center gap-2"
            >
              <Play className="w-4 h-4" />
              Play
            </button>
            <button
              onClick={handleDownload}
              className="flex-1 bg-slate-700 hover:bg-slate-600 text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center gap-2 border border-slate-600"
            >
              <Download className="w-4 h-4" />
              Download
            </button>
          </div>

          {/* Media URL Info */}
          <div className="p-4 bg-slate-900/50 rounded-lg border border-slate-700/30">
            <p className="text-sm text-slate-400 mb-2">Source URL:</p>
            <p className="text-sm text-slate-300 break-all font-mono bg-slate-800/50 p-2 rounded border">
              {mediaUrl}
            </p>
          </div>
        </div>

        {/* Help Text */}
        <div className="text-center mt-4">
          <p className="text-slate-400 text-sm">
            Choose an action for this media file detected from the current page
          </p>
        </div>
      </div>
    </div>
  );
};
