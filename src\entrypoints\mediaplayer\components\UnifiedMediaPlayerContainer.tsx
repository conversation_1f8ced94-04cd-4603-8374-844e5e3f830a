import React, { useState } from "react";
import { MediaFile } from "../MediaPlayerPage";
import { StreamingMediaFile } from "../helper";
import { MediaPlayerContainer } from "./MediaPlayerContainer";

interface UnifiedMediaPlayerContainerProps {
  currentMedia: MediaFile | StreamingMediaFile;
}

/**
 * Unified container that handles both regular and streaming media files
 * This component determines whether to use the regular MediaPlayerContainer
 * or the streaming component based on the media file type
 */
export const UnifiedMediaPlayerContainer = ({
  currentMedia,
}: UnifiedMediaPlayerContainerProps) => {
  const [fallbackMedia, setFallbackMedia] = useState<MediaFile | null>(null);
  const [streamingFailed, setStreamingFailed] = useState(false);

  // Check if this is a streaming media file
  const streamingMedia = currentMedia as StreamingMediaFile;

  // If streaming failed, use fallback media
  if (streamingFailed && fallbackMedia) {
    return <MediaPlayerContainer currentMedia={fallbackMedia} />;
  }

  if (streamingMedia.isStreaming && streamingMedia.streamingComponent) {
    // Render the streaming component with fallback handlers
    const StreamingComponent = streamingMedia.streamingComponent;
    return (
      <StreamingComponent
        onFallback={(media: MediaFile) => {
          setFallbackMedia(media);
          setStreamingFailed(true);
        }}
        onFallbackFailed={(error: Error) => {
          console.error("Both streaming and fallback failed:", error);
          // Could show a final error state here
        }}
      />
    );
  }

  // Fall back to regular media player container
  return <MediaPlayerContainer currentMedia={currentMedia} />;
};
