import { useRef, useState, useCallback } from "react";
import { MediaFile } from "../MediaPlayerPage";
import type { Torrent, WebTorrent } from "webtorrent";
import {
  getFormatInfo,
  isFormatSupported,
  VidStackSupportedMimeType,
} from "../helper";
import { getFileFormatFromUrl } from "@/utils/url.utils";

declare global {
  interface Window {
    WebTorrent: WebTorrent;
  }
}

const client = new window.WebTorrent();

type TorrentStatus = "idle" | "loading" | "streaming" | "error" | "cancelled";

export function useTorrentPlayer(setMediaFile: (mediaFile: MediaFile) => void) {
  const [status, setStatus] = useState<TorrentStatus>("idle");
  const [progress, setProgress] = useState<number>(0);
  const torrentRef = useRef<Torrent | null>(null);

  const cancelTorrent = useCallback(() => {
    if (torrentRef.current) {
      client.remove(torrentRef.current.infoHash);
      torrentRef.current = null;
    }
    setStatus("cancelled");
    setProgress(0);
  }, []);

  const loadTorrent = useCallback(
    (torrentInput: string | File | ArrayBuffer) => {
      setStatus("loading");
      setProgress(0);

      // Clean up existing torrent
      if (torrentRef.current) {
        client.remove(torrentRef.current.infoHash);
        torrentRef.current = null;
      }

      const addTorrent = (input: string | Buffer | File) => {
        client.add(input, (torrent: Torrent) => {
          torrentRef.current = torrent;

          const file = torrent.files.find((f) =>
            /\.(mp4|webm|mp3|m4a|ogg|wav)$/i.test(f.name),
          );

          if (!file) {
            console.error("No playable file found in torrent");
            setStatus("error");
            return;
          }

          file.getBlobURL((err: Error | string | undefined, url?: string) => {
            if (err || !url) {
              console.error(err ?? "Failed to generate blob URL");
              setStatus("error");
              return;
            }

            console.log(url);
            setStatus("streaming");
            const format = getFileFormatFromUrl(file.name);
            if (!format || !isFormatSupported(format)) {
              console.error("Invalid file format");
              setStatus("error");
              return;
            }
            const { mimeType, name: formatName, type } = getFormatInfo(format);
            setMediaFile({
              src: {
                src: url,
                type: mimeType as VidStackSupportedMimeType,
              },
              format: formatName,
              name: file.name,
              size: file.length,
              type,
            });
          });

          torrent.on("download", () => {
            setProgress(Math.min(Math.round(torrent.progress * 100), 100));
          });

          torrent.on("done", () => {
            setProgress(100);
          });
        });
      };

      if (torrentInput instanceof File) {
        void torrentInput.arrayBuffer().then((ab) => {
          addTorrent(ab as unknown as Buffer);
        });
      } else if (typeof torrentInput === "string") {
        addTorrent(torrentInput);
      } else if (torrentInput instanceof ArrayBuffer) {
        addTorrent(torrentInput as unknown as Buffer);
      } else {
        console.error("Invalid torrent input type");
        setStatus("error");
      }
    },
    [setMediaFile],
  );

  return {
    loadTorrent,
    cancelTorrent,
    status,
    progress,
  };
}
