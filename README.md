# 🎵 WebPlayer - Chrome Extension

A powerful Chrome extension with a built-in media player that supports playing both video and audio files from various sources and formats. Features torrent streaming, multi-threaded playback, and on-the-fly format conversion using FFmpeg.js.

![WebPlayer Extension](https://img.shields.io/badge/Chrome-Extension-blue?logo=google-chrome)
![License](https://img.shields.io/badge/License-MIT-green)
![Version](https://img.shields.io/badge/Version-1.0.0-orange)

## ✨ Features

### 🎥 Video Format Support

- **MP4** → Native HTML5 / Vidstack Player
- **M3U8** → HLS.js (integrated with Vidstack)
- **DASH** → Shaka Player (with Vidstack integration)
- **WebM** → Native HTML5 / Vidstack
- **MKV** → FFmpeg.js (convert to MP4) + Vidstack
- **FLV** → flv.js + Vidstack
- **AVI, TS** → FFmpeg.js (convert to supported format)
- **Torrent** → WebTorrent (stream to Vidstack Player)

### 🎧 Audio Format Support

- **MP3** → Native HTML5 / Vidstack
- **WAV** → Native HTML5
- **OGG** → Native HTML5
- **FLAC** → Native HTML5 (limited) / FFmpeg.js
- **AAC** → Native HTML5
- **M4A** → Native HTML5 / FFmpeg.js
- **OPUS** → Native HTML5
- **ALAC, APE** → FFmpeg.js
- **Torrent** → WebTorrent + FFmpeg.js

### 🎨 Player Features

- **Modern UI** using Vidstack Player
- **Multi-format playback** (video + audio)
- **Torrent streaming** (magnet + .torrent support)
- **Multi-threaded stream handling**
- **Built-in FFmpeg.js** for real-time format conversion
- **Drag-and-drop** support for local files
- **Adaptive streaming** (HLS.js & Shaka Player with Vidstack)
- **Full playback controls**: speed, volume, captions, fullscreen
- **Modular architecture**: only load required libraries dynamically

## 🚀 Installation

### Prerequisites

- Node.js (v18 or higher)
- npm or yarn
- Chrome browser

### Development Setup

1. **Clone the repository**

   ```bash
   git clone https://github.com/Ali-hassan-betterlogic/webplayer.git
   cd webplayer
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Start development server**

   ```bash
   npm run dev
   ```

## 📖 Usage

### Loading Media Files

1. **Local Files**
   - Drag and drop video/audio files directly into the player
   - Use the "Load" tab to browse and select files
   - Supports all major video and audio formats

2. **Stream URLs**
   - Paste direct video/audio URLs
   - Supports HLS (M3U8) and DASH streams
   - Enter magnet links for torrent streaming

3. **Torrent Streaming**
   - Paste magnet links or .torrent file URLs
   - Instant streaming powered by WebTorrent
   - No need to download complete files

### Player Controls

- **Play/Pause**: Large center button
- **Seek**: Click and drag on progress bar
- **Volume**: Adjustable volume control
- **Speed**: Variable playback speed
- **Fullscreen**: Toggle fullscreen mode
- **Quality**: Adaptive quality selection for streams

## 🏗️ Architecture

### Core Technologies

- **React 19** - UI framework
- **TypeScript** - Type safety
- **Tailwind CSS** - Styling
- **WXT** - Web Extension Tools framework
- **Vidstack** - Media player UI
- **Zustand** - State management

### Media Libraries

- **HLS.js** - HLS streaming support
- **Shaka Player** - DASH streaming support
- **WebTorrent** - Torrent streaming
- **FFmpeg.js** - Format conversion
- **flv.js** - FLV format support

### Extension Permissions

The extension requires the following permissions:

- `storage` - Save user preferences
- `tabs` - Access to browser tabs
- `activeTab` - Interact with current tab
- `scripting` - Inject scripts when needed
- `webRequest` - Handle media requests
- `webRequestAuthProvider` - Authentication for streams

### Content Security Policy

```javascript
content_security_policy: {
  extension_pages: "script-src 'self' 'wasm-unsafe-eval'; object-src 'self'";
}
```

## 🎯 Key Features Explained

### Torrent Streaming

- Uses WebTorrent for peer-to-peer streaming
- Supports magnet links and .torrent files
- Streams directly to the player without downloading
- Automatic format detection and conversion

### Format Conversion

- FFmpeg.js handles unsupported formats
- Real-time conversion to web-compatible formats
- Multi-threaded processing for better performance
- Automatic quality optimization

### Adaptive Streaming

- HLS.js for M3U8 streams with quality adaptation
- Shaka Player for DASH streams
- Automatic bandwidth detection
- Quality switching based on network conditions

## 🔧 Development

### Available Scripts

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
npm run type-check   # TypeScript type checking
npm run lint         # ESLint code linting
```

### Adding New Formats

1. Add format detection in `helper.ts`
2. Implement conversion logic if needed
3. Update format support display
4. Test with sample files

### Customizing the Player

- Modify `PlayerTab.tsx` for UI changes
- Update `style.css` for custom styling
- Extend `activeTabStore.ts` for new features

## 🙏 Acknowledgments

- [Vidstack](https://vidstack.io/) - Modern media player framework
- [WebTorrent](https://webtorrent.io/) - Torrent streaming library
- [FFmpeg.js](https://github.com/ffmpegwasm/ffmpeg.wasm) - WebAssembly FFmpeg
- [HLS.js](https://github.com/video-dev/hls.js/) - HLS streaming
- [Shaka Player](https://github.com/shaka-project/shaka-player) - DASH streaming

**WebPlayer** - Your all-in-one media player for the web! 🎵
